#!/usr/bin/env python3
"""
Response Accuracy Fix Validation Script
Tests the implemented fixes to ensure they work correctly
"""

import requests
import json
import time
from typing import Dict, List, Any

class ResponseFixValidator:
    def __init__(self, base_url: str = "http://localhost:8080"):
        self.base_url = base_url
        self.test_results = []
    
    def test_prompt_template_usage(self):
        """Test if the system is now using proper prompt templates"""
        print("🔧 Testing Prompt Template Usage...")
        
        test_query = "What environmental research is mentioned in the documents?"
        test_category = "MANUAL"
        
        results = {}
        for mode in ["strict", "balanced", "off"]:
            try:
                response = self._make_query(test_query, test_category, mode)
                metadata = response.get('metadata', {})
                
                results[mode] = {
                    'template_used': metadata.get('prompt_template_used', False),
                    'mode_recorded': metadata.get('anti_hallucination_mode'),
                    'answer_length': len(response.get('answer', '')),
                    'has_citations': '[' in response.get('answer', '') or '<a href=' in response.get('answer', '')
                }
                
                print(f"  {mode}: Template used: {results[mode]['template_used']}, "
                      f"Length: {results[mode]['answer_length']}, "
                      f"Citations: {results[mode]['has_citations']}")
                
            except Exception as e:
                print(f"  {mode}: Error - {str(e)}")
                results[mode] = {"error": str(e)}
        
        return results
    
    def test_citation_processing(self):
        """Test if citations are properly converted to HTML links"""
        print("🔗 Testing Citation Processing...")
        
        test_query = "What documents discuss forest management practices?"
        test_category = "MANUAL"
        
        try:
            response = self._make_query(test_query, test_category, "strict")
            answer = response.get('answer', '')
            
            # Check for different citation patterns
            has_raw_brackets = '[' in answer and '.pdf]' in answer
            has_html_links = '<a href=' in answer
            has_download_links = '/download_gated/' in answer
            has_proper_styling = 'text-blue-600' in answer
            
            print(f"  Raw bracket citations: {has_raw_brackets}")
            print(f"  HTML links: {has_html_links}")
            print(f"  Download links: {has_download_links}")
            print(f"  Proper styling: {has_proper_styling}")
            
            # Extract a sample citation if present
            if has_html_links:
                import re
                link_pattern = r'<a href="([^"]+)"[^>]*>([^<]+)</a>'
                links = re.findall(link_pattern, answer)
                if links:
                    print(f"  Sample link: {links[0][1]} -> {links[0][0]}")
            
            return {
                'raw_brackets': has_raw_brackets,
                'html_links': has_html_links,
                'download_links': has_download_links,
                'proper_styling': has_proper_styling,
                'answer_preview': answer[:300]
            }
            
        except Exception as e:
            print(f"  Error testing citations: {str(e)}")
            return {"error": str(e)}
    
    def test_hallucination_detection(self):
        """Test if hallucination detection is working"""
        print("🧠 Testing Hallucination Detection...")
        
        test_query = "What is the population of Mars?"  # Should trigger insufficient info
        test_category = "MANUAL"
        
        results = {}
        for mode in ["strict", "balanced"]:
            try:
                response = self._make_query(test_query, test_category, mode)
                metadata = response.get('metadata', {})
                hallucination_check = metadata.get('hallucination_check', {})
                
                results[mode] = {
                    'hallucination_detected': hallucination_check.get('detected', False),
                    'warning': hallucination_check.get('warning'),
                    'answer_length': len(response.get('answer', '')),
                    'has_insufficient_response': any(phrase in response.get('answer', '').lower() 
                                                   for phrase in ['enough information', 'cannot find', 'not found'])
                }
                
                print(f"  {mode}: Detected: {results[mode]['hallucination_detected']}, "
                      f"Insufficient: {results[mode]['has_insufficient_response']}")
                
            except Exception as e:
                print(f"  {mode}: Error - {str(e)}")
                results[mode] = {"error": str(e)}
        
        return results
    
    def test_mode_differences(self):
        """Test if different anti-hallucination modes produce different responses"""
        print("⚖️ Testing Mode Differences...")
        
        test_query = "What can you tell me about forest conservation strategies?"
        test_category = "MANUAL"
        
        responses = {}
        for mode in ["strict", "balanced", "off"]:
            try:
                response = self._make_query(test_query, test_category, mode)
                responses[mode] = response.get('answer', '')
                print(f"  {mode}: {len(responses[mode])} characters")
            except Exception as e:
                print(f"  {mode}: Error - {str(e)}")
                responses[mode] = f"Error: {str(e)}"
        
        # Check if responses are different
        unique_responses = len(set(responses.values()))
        print(f"  Unique responses: {unique_responses}/3")
        
        if unique_responses == 1:
            print("  ⚠️  All modes produced identical responses")
        elif unique_responses == 3:
            print("  ✅ All modes produced different responses")
        else:
            print("  ⚠️  Some modes produced identical responses")
        
        return {
            'responses': responses,
            'unique_count': unique_responses,
            'modes_working': unique_responses > 1
        }
    
    def test_scientific_name_formatting(self):
        """Test if scientific names are properly formatted"""
        print("🔬 Testing Scientific Name Formatting...")
        
        test_query = "What species are mentioned in the research documents?"
        test_category = "MANUAL"
        
        try:
            response = self._make_query(test_query, test_category, "balanced")
            answer = response.get('answer', '')
            
            # Look for italic formatting patterns
            has_asterisk_italics = '*' in answer
            has_species_mentions = any(word in answer.lower() for word in ['species', 'genus', 'family'])
            
            # Look for common scientific name patterns
            import re
            binomial_pattern = r'\*[A-Z][a-z]+ [a-z]+\*'
            binomial_matches = re.findall(binomial_pattern, answer)
            
            print(f"  Asterisk italics found: {has_asterisk_italics}")
            print(f"  Species mentions: {has_species_mentions}")
            print(f"  Binomial patterns: {len(binomial_matches)}")
            
            if binomial_matches:
                print(f"  Sample: {binomial_matches[0]}")
            
            return {
                'has_italics': has_asterisk_italics,
                'has_species_mentions': has_species_mentions,
                'binomial_count': len(binomial_matches),
                'binomial_examples': binomial_matches[:3]
            }
            
        except Exception as e:
            print(f"  Error testing scientific names: {str(e)}")
            return {"error": str(e)}
    
    def _make_query(self, question: str, category: str, mode: str) -> Dict[str, Any]:
        """Make a query to the AI agent"""
        url = f"{self.base_url}/api/query"
        
        payload = {
            "question": question,
            "category": category,
            "anti_hallucination_mode": mode
        }
        
        response = requests.post(url, json=payload, timeout=30)
        response.raise_for_status()
        return response.json()
    
    def run_validation(self):
        """Run all validation tests"""
        print("🚀 Starting Response Accuracy Fix Validation\n")
        
        results = {
            "timestamp": time.time(),
            "prompt_templates": self.test_prompt_template_usage(),
            "citation_processing": self.test_citation_processing(),
            "hallucination_detection": self.test_hallucination_detection(),
            "mode_differences": self.test_mode_differences(),
            "scientific_names": self.test_scientific_name_formatting()
        }
        
        print("\n📋 Validation Summary:")
        self._print_validation_summary(results)
        
        return results
    
    def _print_validation_summary(self, results: Dict):
        """Print a summary of validation results"""
        print("  🔧 Prompt Templates:")
        template_results = results.get("prompt_templates", {})
        template_working = any(r.get('template_used', False) for r in template_results.values() if isinstance(r, dict))
        print(f"    {'✅' if template_working else '❌'} Template system: {'Working' if template_working else 'Not working'}")
        
        print("  🔗 Citation Processing:")
        citation_results = results.get("citation_processing", {})
        citations_working = citation_results.get('html_links', False) and citation_results.get('download_links', False)
        print(f"    {'✅' if citations_working else '❌'} HTML links: {'Working' if citations_working else 'Not working'}")
        
        print("  🧠 Hallucination Detection:")
        halluc_results = results.get("hallucination_detection", {})
        halluc_working = any(r.get('has_insufficient_response', False) for r in halluc_results.values() if isinstance(r, dict))
        print(f"    {'✅' if halluc_working else '❌'} Detection: {'Working' if halluc_working else 'Not working'}")
        
        print("  ⚖️ Mode Differences:")
        mode_results = results.get("mode_differences", {})
        modes_working = mode_results.get('modes_working', False)
        print(f"    {'✅' if modes_working else '❌'} Different modes: {'Working' if modes_working else 'Not working'}")

if __name__ == "__main__":
    validator = ResponseFixValidator()
    results = validator.run_validation()
    
    # Save results
    with open("validation_results.json", "w") as f:
        json.dump(results, f, indent=2)
    
    print(f"\n💾 Validation results saved to validation_results.json")
