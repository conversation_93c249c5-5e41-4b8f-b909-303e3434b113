#!/usr/bin/env python3
"""
AI Agent Response Accuracy Diagnostic Tool
Tests various query types to identify response accuracy issues
"""

import requests
import json
import time
from typing import Dict, List, Any

class ResponseAccuracyTester:
    def __init__(self, base_url: str = "http://localhost:8080"):
        self.base_url = base_url
        self.test_results = []
    
    def test_anti_hallucination_modes(self):
        """Test if different anti-hallucination modes produce different responses"""
        print("🧪 Testing Anti-Hallucination Modes...")
        
        test_query = "What are the main environmental impacts of deforestation?"
        test_category = "MANUAL"  # Adjust based on available categories
        
        modes = ["strict", "balanced", "off"]
        responses = {}
        
        for mode in modes:
            print(f"  Testing {mode} mode...")
            try:
                response = self._make_query(test_query, test_category, mode)
                responses[mode] = response
                print(f"    ✓ {mode}: {len(response.get('answer', ''))} chars")
            except Exception as e:
                print(f"    ✗ {mode}: Error - {str(e)}")
                responses[mode] = {"error": str(e)}
        
        # Analyze differences
        self._analyze_mode_differences(responses)
        return responses
    
    def test_citation_formatting(self):
        """Test if citations are properly formatted as HTML links"""
        print("🔗 Testing Citation Formatting...")
        
        test_query = "What is mentioned in the documents about forest management?"
        test_category = "MANUAL"
        
        try:
            response = self._make_query(test_query, test_category, "strict")
            answer = response.get('answer', '')
            
            # Check for citation patterns
            has_raw_citations = '[' in answer and '.pdf]' in answer
            has_html_links = '<a href=' in answer
            has_proper_links = '/download_gated/' in answer
            
            print(f"    Raw citations found: {has_raw_citations}")
            print(f"    HTML links found: {has_html_links}")
            print(f"    Proper download links: {has_proper_links}")
            
            if has_raw_citations and not has_html_links:
                print("    ⚠️  ISSUE: Citations not converted to HTML links")
            elif has_html_links and has_proper_links:
                print("    ✓ Citations properly formatted")
            
            return {
                "raw_citations": has_raw_citations,
                "html_links": has_html_links,
                "proper_links": has_proper_links,
                "answer_preview": answer[:200]
            }
        except Exception as e:
            print(f"    ✗ Error testing citations: {str(e)}")
            return {"error": str(e)}
    
    def test_scientific_name_formatting(self):
        """Test if scientific names are properly formatted"""
        print("🔬 Testing Scientific Name Formatting...")
        
        test_query = "What species are mentioned in the documents?"
        test_category = "MANUAL"
        
        try:
            response = self._make_query(test_query, test_category, "balanced")
            answer = response.get('answer', '')
            
            # Look for scientific name patterns
            has_italics = '*' in answer and 'species' in answer.lower()
            has_binomial = any(word.count(' ') == 1 and word[0].isupper() 
                             for word in answer.split() if len(word) > 5)
            
            print(f"    Italic formatting found: {has_italics}")
            print(f"    Binomial patterns found: {has_binomial}")
            
            return {
                "has_italics": has_italics,
                "has_binomial": has_binomial,
                "answer_preview": answer[:200]
            }
        except Exception as e:
            print(f"    ✗ Error testing scientific names: {str(e)}")
            return {"error": str(e)}
    
    def test_context_adherence(self):
        """Test if responses stick to provided context"""
        print("📋 Testing Context Adherence...")
        
        # Test with a very specific query that should have limited context
        test_query = "What is the capital of Mars?"  # Should not be in documents
        test_category = "MANUAL"
        
        try:
            response = self._make_query(test_query, test_category, "strict")
            answer = response.get('answer', '')
            
            insufficient_phrases = [
                "don't have enough information",
                "not found in the context",
                "cannot find information",
                "insufficient information"
            ]
            
            has_insufficient_response = any(phrase in answer.lower() 
                                          for phrase in insufficient_phrases)
            
            print(f"    Proper 'insufficient info' response: {has_insufficient_response}")
            print(f"    Answer: {answer[:100]}...")
            
            return {
                "proper_insufficient_response": has_insufficient_response,
                "answer": answer
            }
        except Exception as e:
            print(f"    ✗ Error testing context adherence: {str(e)}")
            return {"error": str(e)}
    
    def _make_query(self, question: str, category: str, mode: str) -> Dict[str, Any]:
        """Make a query to the AI agent"""
        url = f"{self.base_url}/api/query"
        
        payload = {
            "question": question,
            "category": category,
            "anti_hallucination_mode": mode
        }
        
        response = requests.post(url, json=payload, timeout=30)
        response.raise_for_status()
        return response.json()
    
    def _analyze_mode_differences(self, responses: Dict[str, Dict]):
        """Analyze differences between anti-hallucination modes"""
        print("    📊 Mode Analysis:")
        
        # Check if responses are actually different
        answers = {mode: resp.get('answer', '') for mode, resp in responses.items() 
                  if 'error' not in resp}
        
        if len(set(answers.values())) == 1:
            print("    ⚠️  ISSUE: All modes produced identical responses")
        else:
            print("    ✓ Modes produced different responses")
        
        # Check response lengths
        for mode, answer in answers.items():
            print(f"      {mode}: {len(answer)} characters")
    
    def run_full_diagnostic(self):
        """Run all diagnostic tests"""
        print("🚀 Starting AI Agent Response Accuracy Diagnostic\n")
        
        results = {
            "timestamp": time.time(),
            "anti_hallucination_modes": self.test_anti_hallucination_modes(),
            "citation_formatting": self.test_citation_formatting(),
            "scientific_name_formatting": self.test_scientific_name_formatting(),
            "context_adherence": self.test_context_adherence()
        }
        
        print("\n📋 Diagnostic Summary:")
        self._print_summary(results)
        
        return results
    
    def _print_summary(self, results: Dict):
        """Print a summary of diagnostic results"""
        issues = []
        
        # Check anti-hallucination modes
        modes_data = results.get("anti_hallucination_modes", {})
        if isinstance(modes_data, dict):
            answers = {k: v.get('answer', '') for k, v in modes_data.items() 
                      if isinstance(v, dict) and 'error' not in v}
            if len(set(answers.values())) <= 1:
                issues.append("Anti-hallucination modes not working")
        
        # Check citations
        citations_data = results.get("citation_formatting", {})
        if citations_data.get("raw_citations") and not citations_data.get("html_links"):
            issues.append("Citations not converted to HTML links")
        
        # Check context adherence
        context_data = results.get("context_adherence", {})
        if not context_data.get("proper_insufficient_response"):
            issues.append("Not properly handling insufficient context")
        
        if issues:
            print("  ❌ Issues Found:")
            for issue in issues:
                print(f"    - {issue}")
        else:
            print("  ✅ No major issues detected")

if __name__ == "__main__":
    tester = ResponseAccuracyTester()
    results = tester.run_full_diagnostic()
    
    # Save results to file
    with open("response_accuracy_results.json", "w") as f:
        json.dump(results, f, indent=2)
    
    print(f"\n💾 Results saved to response_accuracy_results.json")
