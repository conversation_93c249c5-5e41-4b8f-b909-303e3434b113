#!/usr/bin/env python3
"""
Model Optimizer Service

Automatically optimizes query parameters and model settings based on the selected LLM model.
Provides dynamic configuration selection for optimal PDF RAG performance.
"""

import json
import os
import logging
from typing import Dict, Any, Tuple, Optional
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class ModelProfile:
    """Profile information for a specific model"""
    name: str
    category: str  # large, medium, small, compact
    context_size: int
    optimal_temperature_range: Tuple[float, float]
    recommended_top_k: int
    recommended_top_p: float
    repeat_penalty: float
    retrieval_k: int
    relevance_threshold: float
    strengths: list
    limitations: list

class ModelOptimizer:
    """Service for optimizing model parameters for PDF RAG tasks"""
    
    def __init__(self):
        self.model_profiles = self._load_model_profiles()
        self.base_config_path = 'config/optimized_rag_configs.json'
        
    def _load_model_profiles(self) -> Dict[str, ModelProfile]:
        """Load model profiles with optimization parameters"""
        profiles = {}
        
        # Large models (14B+)
        profiles["qwen3:14b-q4_K_M"] = ModelProfile(
            name="qwen3:14b-q4_K_M",
            category="large",
            context_size=8192,
            optimal_temperature_range=(0.05, 0.7),
            recommended_top_k=25,
            recommended_top_p=0.8,
            repeat_penalty=1.1,
            retrieval_k=15,
            relevance_threshold=0.12,
            strengths=["Excellent reasoning", "Strong context understanding", "Good technical writing"],
            limitations=["Higher resource usage", "Slower inference"]
        )
        
        # Medium models (8B-12B)
        medium_models = [
            "llama3.1:8b-instruct-q8_0", "llama3.1:8b-instruct-q4_K_M",
            "deepseek-r1:8b-llama-distill-q8_0", "deepseek-r1:8b-llama-distill-q4_K_M",
            "gemma3:12b-it-q4_K_M"
        ]
        for model in medium_models:
            profiles[model] = ModelProfile(
                name=model,
                category="medium",
                context_size=6144,
                optimal_temperature_range=(0.1, 0.8),
                recommended_top_k=30,
                recommended_top_p=0.85,
                repeat_penalty=1.15,
                retrieval_k=12,
                relevance_threshold=0.15,
                strengths=["Balanced performance", "Good instruction following", "Reasonable speed"],
                limitations=["Limited context compared to large models"]
            )
        
        # Small efficient models (4B-7B)
        small_models = [
            "mistral:7b-instruct-v0.3-q8_0", "mistral:7b-instruct-v0.3-q4_K_M",
            "gemma3:4b-it-q4_K_M"
        ]
        for model in small_models:
            profiles[model] = ModelProfile(
                name=model,
                category="small",
                context_size=4096,
                optimal_temperature_range=(0.15, 0.9),
                recommended_top_k=35,
                recommended_top_p=0.88,
                repeat_penalty=1.2,
                retrieval_k=10,
                relevance_threshold=0.18,
                strengths=["Fast inference", "Low memory usage", "Good efficiency"],
                limitations=["May struggle with complex reasoning", "Limited context window"]
            )
        
        # Compact models (3B and below)
        profiles["llama3.2:3b-instruct-q4_K_M"] = ModelProfile(
            name="llama3.2:3b-instruct-q4_K_M",
            category="compact",
            context_size=3072,
            optimal_temperature_range=(0.2, 1.0),
            recommended_top_k=40,
            recommended_top_p=0.9,
            repeat_penalty=1.25,
            retrieval_k=8,
            relevance_threshold=0.22,
            strengths=["Very fast", "Very low memory", "Decent for size"],
            limitations=["Limited reasoning ability", "Small context window", "May miss nuances"]
        )
        
        # Problematic models
        profiles["gpt-oss:20b"] = ModelProfile(
            name="gpt-oss:20b",
            category="problematic",
            context_size=4096,
            optimal_temperature_range=(0.3, 0.7),
            recommended_top_k=20,
            recommended_top_p=0.8,
            repeat_penalty=1.1,
            retrieval_k=10,
            relevance_threshold=0.2,
            strengths=["Large parameter count"],
            limitations=["Inconsistent results", "Poor instruction following", "Resource intensive without benefits"]
        )
        
        return profiles
    
    def get_model_profile(self, model_name: str) -> Optional[ModelProfile]:
        """Get the profile for a specific model"""
        return self.model_profiles.get(model_name)
    
    def optimize_parameters_for_model(self, model_name: str, anti_hallucination_mode: str = "balanced") -> Dict[str, Any]:
        """Get optimized parameters for a specific model and mode"""
        profile = self.get_model_profile(model_name)
        
        if not profile:
            logger.warning(f"No profile found for model {model_name}, using default parameters")
            return self._get_default_parameters(anti_hallucination_mode)
        
        # Get temperature based on mode and model profile
        temp_min, temp_max = profile.optimal_temperature_range
        if anti_hallucination_mode == "strict":
            temperature = temp_min
        elif anti_hallucination_mode == "balanced":
            temperature = temp_min + (temp_max - temp_min) * 0.4
        else:  # off mode
            temperature = temp_min + (temp_max - temp_min) * 0.8
        
        # Get top_k based on mode
        if anti_hallucination_mode == "strict":
            top_k = 1
        elif anti_hallucination_mode == "balanced":
            top_k = profile.recommended_top_k
        else:  # off mode
            top_k = int(profile.recommended_top_k * 1.5)
        
        # Get top_p based on mode
        if anti_hallucination_mode == "strict":
            top_p = max(0.6, profile.recommended_top_p - 0.2)
        elif anti_hallucination_mode == "balanced":
            top_p = profile.recommended_top_p
        else:  # off mode
            top_p = min(0.95, profile.recommended_top_p + 0.1)
        
        # Calculate num_predict based on context size
        if profile.context_size >= 8192:
            num_predict = 2048
        elif profile.context_size >= 6144:
            num_predict = 1536
        elif profile.context_size >= 4096:
            num_predict = 1024
        else:
            num_predict = 768
        
        # Get stop tokens based on model family
        stop_tokens = self._get_stop_tokens_for_model(model_name)
        
        # Generate system prompt based on model capability and mode
        system_prompt = self._generate_system_prompt(profile, anti_hallucination_mode)
        
        return {
            "temperature": temperature,
            "num_ctx": profile.context_size,
            "num_predict": num_predict,
            "top_p": top_p,
            "top_k": top_k,
            "repeat_penalty": profile.repeat_penalty,
            "stop": stop_tokens,
            "system_prompt": system_prompt
        }
    
    def get_optimized_query_parameters(self, model_name: str) -> Dict[str, Any]:
        """Get optimized query parameters for a specific model"""
        profile = self.get_model_profile(model_name)
        
        if not profile:
            logger.warning(f"No profile found for model {model_name}, using default query parameters")
            return {
                "retrieval_k": 12,
                "relevance_threshold": 0.15,
                "min_documents": 3,
                "max_documents": 8,
                "max_vision_context_length": 2000
            }
        
        # Adjust max_documents based on model category
        if profile.category == "large":
            max_documents = 10
            max_vision_context = 3000
        elif profile.category == "medium":
            max_documents = 8
            max_vision_context = 2500
        elif profile.category == "small":
            max_documents = 6
            max_vision_context = 2000
        else:  # compact
            max_documents = 5
            max_vision_context = 1500
        
        return {
            "retrieval_k": profile.retrieval_k,
            "relevance_threshold": profile.relevance_threshold,
            "min_documents": min(3, max_documents - 2),
            "max_documents": max_documents,
            "max_vision_context_length": max_vision_context
        }
    
    def _get_stop_tokens_for_model(self, model_name: str) -> list:
        """Get appropriate stop tokens for the model"""
        if "llama" in model_name.lower():
            return ["<|endoftext|>", "<|im_end|>", "Human:", "Question:"]
        elif "mistral" in model_name.lower():
            return ["<|endoftext|>", "[INST]", "</s>"]
        elif "gemma" in model_name.lower():
            return ["<|endoftext|>", "<|im_end|>"]
        elif "qwen" in model_name.lower():
            return ["<|endoftext|>", "<|im_end|>", "\n\nHuman:", "\n\nQuestion:"]
        elif "deepseek" in model_name.lower():
            return ["<|endoftext|>", "<|im_end|>", "Human:", "Question:"]
        else:
            return ["<|endoftext|>", "Human:", "Question:"]
    
    def _generate_system_prompt(self, profile: ModelProfile, mode: str) -> str:
        """Generate optimized system prompt based on model profile and mode"""
        base_prompts = {
            "strict": "You are a precise research assistant for ERDB. Answer EXCLUSIVELY based on provided context. ALWAYS cite sources using [filename.pdf]. Be accurate and factual.",
            "balanced": "You are a knowledgeable ERDB assistant. Answer primarily from context with careful inference. ALWAYS cite sources using [filename.pdf]. Be helpful and informative.",
            "off": "You are an expert ERDB consultant. Use context as foundation, supplement with expertise. Cite context sources [filename.pdf]. Distinguish between context and general knowledge."
        }
        
        base_prompt = base_prompts.get(mode, base_prompts["balanced"])
        
        # Add model-specific optimizations
        if profile.category == "large":
            base_prompt += " Provide comprehensive technical explanations when appropriate."
        elif profile.category == "compact":
            base_prompt += " Be concise and focus on key points."
        
        # Add scientific name formatting instruction
        base_prompt += " Format scientific names in markdown italics (*Genus species*)."
        
        return base_prompt
    
    def _get_default_parameters(self, mode: str) -> Dict[str, Any]:
        """Get default parameters when no model profile is available"""
        defaults = {
            "strict": {
                "temperature": 0.1,
                "num_ctx": 4096,
                "num_predict": 1024,
                "top_p": 0.7,
                "top_k": 1,
                "repeat_penalty": 1.2,
                "stop": ["<|endoftext|>", "Human:"],
                "system_prompt": "Answer ONLY from provided context. Cite sources [filename.pdf]. Be accurate."
            },
            "balanced": {
                "temperature": 0.5,
                "num_ctx": 5120,
                "num_predict": 1536,
                "top_p": 0.9,
                "top_k": 40,
                "repeat_penalty": 1.1,
                "stop": ["<|endoftext|>"],
                "system_prompt": "Answer from context with careful inference. Cite sources [filename.pdf]. Be helpful."
            },
            "off": {
                "temperature": 1.0,
                "num_ctx": 5120,
                "num_predict": 2048,
                "top_p": 0.95,
                "top_k": 64,
                "repeat_penalty": 1.0,
                "stop": [],
                "system_prompt": "Use context and knowledge. Cite context sources [filename.pdf]. Be informative."
            }
        }
        return defaults.get(mode, defaults["balanced"])
    
    def get_model_recommendations(self) -> Dict[str, Any]:
        """Get model recommendations for different use cases"""
        return {
            "best_overall": "qwen3:14b-q4_K_M",
            "best_performance": "qwen3:14b-q4_K_M",
            "best_balanced": "llama3.1:8b-instruct-q8_0",
            "best_speed": "mistral:7b-instruct-v0.3-q8_0",
            "best_efficiency": "gemma3:4b-it-q4_K_M",
            "best_low_memory": "llama3.2:3b-instruct-q4_K_M",
            "not_recommended": ["gpt-oss:20b"]
        }
    
    def analyze_model_suitability(self, model_name: str) -> Dict[str, Any]:
        """Analyze how suitable a model is for PDF RAG tasks"""
        profile = self.get_model_profile(model_name)
        
        if not profile:
            return {
                "suitable": False,
                "reason": "Unknown model profile",
                "recommendations": ["Use a supported model from the list"]
            }
        
        if profile.category == "problematic":
            return {
                "suitable": False,
                "reason": "Known issues with this model",
                "limitations": profile.limitations,
                "recommendations": ["Consider qwen3:14b-q4_K_M or llama3.1:8b-instruct-q8_0 instead"]
            }
        
        suitability_scores = {
            "large": 95,
            "medium": 85,
            "small": 70,
            "compact": 60
        }
        
        score = suitability_scores.get(profile.category, 50)
        
        return {
            "suitable": score >= 60,
            "suitability_score": score,
            "category": profile.category,
            "strengths": profile.strengths,
            "limitations": profile.limitations,
            "optimal_use_cases": self._get_optimal_use_cases(profile.category)
        }
    
    def _get_optimal_use_cases(self, category: str) -> list:
        """Get optimal use cases for model category"""
        use_cases = {
            "large": ["Complex technical documents", "Research papers", "Detailed analysis", "Multi-document synthesis"],
            "medium": ["General PDF RAG", "Balanced performance needs", "Most common use cases"],
            "small": ["Simple queries", "Fast responses", "Resource-constrained environments"],
            "compact": ["Basic questions", "Very limited resources", "Simple fact extraction"]
        }
        return use_cases.get(category, ["General use"])

# Global optimizer instance
_optimizer = None

def get_model_optimizer() -> ModelOptimizer:
    """Get the global model optimizer instance"""
    global _optimizer
    if _optimizer is None:
        _optimizer = ModelOptimizer()
    return _optimizer
