#!/usr/bin/env python3
"""
Test the corrected link protection logic
"""

import re

def test_correct_link_protection():
    print("Testing corrected citation link protection...")
    
    # Test content with citation link
    test_content = 'According to <a href="/download_gated/file.pdf" class="text-blue-600">file.pdf</a>, this is markdown **bold** text.'
    
    # Simulate the corrected JavaScript logic
    protected_links = []
    
    def replace_link(match):
        placeholder = f"__PROTECTED_LINK_{len(protected_links)}__"
        protected_links.append(match.group(0))
        return placeholder
    
    # Protect links (corrected version)
    protected_content = re.sub(r'<a[^>]*href="[^"]*"[^>]*>[^<]*</a>', replace_link, test_content)
    
    print(f"Original: {test_content}")
    print(f"Protected: {protected_content}")
    print(f"Protected links: {protected_links}")
    
    # Simulate markdown processing (would make **bold** into <strong>bold</strong>)
    processed = protected_content.replace('**bold**', '<strong>bold</strong>')
    print(f"After markdown: {processed}")
    
    # Restore links (this should work now)
    for i, link in enumerate(protected_links):
        processed = processed.replace(f"__PROTECTED_LINK_{i}__", link)
    
    print(f"Final restored: {processed}")
    
    # Check results
    link_preserved = '<a href="/download_gated/file.pdf"' in processed and 'file.pdf</a>' in processed
    markdown_processed = '<strong>bold</strong>' in processed
    placeholders_removed = '__PROTECTED_LINK_' not in processed
    
    print(f"Link preserved: {link_preserved}")
    print(f"Markdown processed: {markdown_processed}")
    print(f"Placeholders removed: {placeholders_removed}")
    
    return link_preserved and markdown_processed and placeholders_removed

if __name__ == "__main__":
    print("🧪 Testing Corrected Link Protection")
    print("=" * 50)
    
    success = test_correct_link_protection()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ Link protection fix working correctly!")
    else:
        print("❌ Link protection still has issues")
