#!/usr/bin/env python3
"""
Implementation Comparison Tool
Compares current vs expected AI agent implementations
"""

import json
import os
from typing import Dict, Any

def load_config():
    """Load the default models configuration"""
    try:
        with open('config/default_models.json', 'r') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading config: {e}")
        return {}

def analyze_current_implementation():
    """Analyze the current query_service.py implementation"""
    print("🔍 Analyzing Current Implementation (query_service.py)")
    
    issues = []
    
    # Read current implementation
    try:
        with open('app/services/query_service.py', 'r') as f:
            content = f.read()
        
        # Check for hardcoded prompt
        if 'Based on the following context, answer the question' in content:
            issues.append("❌ Using hardcoded basic prompt instead of config templates")
        
        # Check for anti-hallucination mode usage
        if 'anti_hallucination_mode' in content:
            if 'prompt_templates' not in content:
                issues.append("❌ Anti-hallucination mode parameter ignored in prompt generation")
        
        # Check for citation processing
        if 'citation' in content.lower():
            if 'post-processing' not in content.lower() and 'convert' not in content.lower():
                issues.append("❌ No citation post-processing found")
        
        # Check for template usage
        if 'default_models.json' in content:
            if 'prompt_templates' not in content:
                issues.append("❌ Config loaded but prompt templates not used")
        
        print(f"  Found {len(issues)} issues:")
        for issue in issues:
            print(f"    {issue}")
            
    except Exception as e:
        print(f"  Error reading current implementation: {e}")
        issues.append(f"❌ Cannot read current implementation: {e}")
    
    return issues

def analyze_backup_implementation():
    """Analyze the backup query_service.py.bak implementation"""
    print("\n🔍 Analyzing Backup Implementation (query_service.py.bak)")
    
    features = []
    
    try:
        with open('app/services/query_service.py.bak', 'r') as f:
            content = f.read()
        
        # Check for proper template usage
        if 'ChatPromptTemplate' in content:
            features.append("✅ Uses proper prompt templates")
        
        # Check for hallucination detection
        if 'hallucination_detected' in content:
            features.append("✅ Has hallucination detection")
        
        # Check for citation processing
        if 'post-processing' in content.lower():
            features.append("✅ Has post-processing for citations")
        
        # Check for mode-specific behavior
        if 'anti_hallucination_mode' in content and 'balanced' in content:
            features.append("✅ Implements mode-specific behavior")
        
        print(f"  Found {len(features)} proper features:")
        for feature in features:
            print(f"    {feature}")
            
    except Exception as e:
        print(f"  Error reading backup implementation: {e}")
        features.append(f"❌ Cannot read backup implementation: {e}")
    
    return features

def compare_prompt_templates():
    """Compare expected vs actual prompt usage"""
    print("\n📝 Comparing Prompt Templates")
    
    config = load_config()
    templates = config.get('query_parameters', {}).get('prompt_templates', {})
    
    if not templates:
        print("  ❌ No prompt templates found in config")
        return
    
    print(f"  ✅ Found {len(templates)} prompt templates in config:")
    for mode in templates.keys():
        template = templates[mode]
        features = []
        
        if 'CITATION GUIDELINES' in template:
            features.append("Citation guidelines")
        if 'SCIENTIFIC NAME FORMATTING' in template:
            features.append("Scientific name formatting")
        if 'ANTI-HALLUCINATION' in template:
            features.append("Anti-hallucination instructions")
        if 'VERIFICATION PROCESS' in template:
            features.append("Verification process")
        
        print(f"    {mode}: {', '.join(features) if features else 'Basic template'}")
    
    # Check current usage
    try:
        with open('app/services/query_service.py', 'r') as f:
            current_content = f.read()
        
        if any(mode in current_content for mode in templates.keys()):
            print("  ✅ Current implementation references template modes")
        else:
            print("  ❌ Current implementation doesn't use config templates")
            
    except Exception as e:
        print(f"  Error checking current template usage: {e}")

def generate_fix_recommendations():
    """Generate specific fix recommendations"""
    print("\n🛠 Fix Recommendations")
    
    recommendations = [
        {
            "priority": "CRITICAL",
            "issue": "Replace hardcoded prompt with config templates",
            "action": "Modify query_service.py to load and use prompt templates from default_models.json",
            "file": "app/services/query_service.py",
            "lines": "485-492"
        },
        {
            "priority": "CRITICAL", 
            "issue": "Implement anti-hallucination mode logic",
            "action": "Add logic to select appropriate prompt template based on anti_hallucination_mode parameter",
            "file": "app/services/query_service.py",
            "lines": "350-362"
        },
        {
            "priority": "HIGH",
            "issue": "Add citation post-processing",
            "action": "Implement post-processing to convert [filename.pdf] to HTML links",
            "file": "app/services/query_service.py", 
            "lines": "After line 500"
        },
        {
            "priority": "HIGH",
            "issue": "Restore proper implementation",
            "action": "Consider restoring query_service.py.bak which has proper implementation",
            "file": "app/services/query_service.py",
            "lines": "Entire file"
        },
        {
            "priority": "MEDIUM",
            "issue": "Add hallucination detection",
            "action": "Implement hallucination detection logic from backup file",
            "file": "app/services/query_service.py",
            "lines": "After response generation"
        }
    ]
    
    for i, rec in enumerate(recommendations, 1):
        print(f"  {i}. [{rec['priority']}] {rec['issue']}")
        print(f"     Action: {rec['action']}")
        print(f"     File: {rec['file']} (lines {rec['lines']})")
        print()

def main():
    """Run the complete comparison analysis"""
    print("🔬 AI Agent Implementation Comparison Tool\n")
    
    current_issues = analyze_current_implementation()
    backup_features = analyze_backup_implementation()
    compare_prompt_templates()
    generate_fix_recommendations()
    
    print("\n📊 Summary:")
    print(f"  Current implementation issues: {len(current_issues)}")
    print(f"  Backup implementation features: {len(backup_features)}")
    
    if current_issues:
        print("\n⚠️  The current implementation has significant issues that need to be fixed.")
        print("   Consider applying the recommended fixes or restoring from backup.")
    else:
        print("\n✅ Current implementation looks good!")

if __name__ == "__main__":
    main()
