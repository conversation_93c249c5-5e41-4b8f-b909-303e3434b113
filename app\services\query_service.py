from langchain_ollama import ChatOllama
from langchain_ollama import OllamaEmbeddings
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import StrOutputParser
from app.services.vector_db import get_vector_db, CHROMA_PATH
from langchain_chroma import Chroma
import logging
import os
import json
import re
import requests
from datetime import datetime
import time
from bs4 import BeautifulSoup
from urllib.parse import urljoin
from langchain_core.vectorstores import VectorStoreRetriever
from langchain_core.documents import Document
import numpy as np
from typing import List, Dict, Any, Tuple
from concurrent.futures import ThreadPoolExecutor, as_completed
# Import the vision processor module
from app.services import vision_processor
# Import the query configuration system
from config.settings.query_config import get_query_config, QueryConfiguration
from app.utils.content_db import get_source_url_by_id
# Import enhanced caching system
from app.services.cache_service import QueryCache
# Import RAG performance monitoring
from app.utils.rag_performance import get_rag_monitor
from app.services.vector_db import similarity_search_with_category_filter
from app.utils.helpers import list_categories
# COMMENTED OUT: No longer using backend scientific name formatting
# from app.utils.validate_and_italicize_scientific_names import validate_and_italicize_scientific_names
from app.utils.config import DEFAULT_MODELS_FILE

# Import performance monitoring decorators
from app.utils.performance_monitor import (
    performance_monitor,
    get_performance_monitor
)
from app.utils.chroma_performance import monitor_similarity_search

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def process_citations(answer: str, docs: List[Document]) -> str:
    """
    Process citation placeholders in the answer and convert them to HTML links.

    Args:
        answer: The AI-generated answer containing citation placeholders
        docs: List of documents that were used as context

    Returns:
        Answer with citation placeholders converted to HTML links
    """
    if not answer or not docs:
        return answer

    # Create comprehensive mapping of citation formats to document metadata
    citation_map = {}
    for doc in docs:
        if hasattr(doc, 'metadata'):
            citation_filename = doc.metadata.get('citation_filename',
                                               doc.metadata.get('original_filename',
                                               doc.metadata.get('filename', 'Unknown')))
            source_name = doc.metadata.get('source', citation_filename)
            page = doc.metadata.get('page', 1)
            doc_type = doc.metadata.get('type', 'pdf')
            
            # Get the display title (for documents like "CANOPY INTERNATIONAL • VOL.48 NO.1")
            display_title = doc.metadata.get('title', citation_filename)

            if citation_filename and citation_filename != 'Unknown':
                doc_info = {
                    'source': source_name,
                    'page': page,
                    'type': doc_type,
                    'display_name': citation_filename,
                    'display_title': display_title
                }
                
                # Map multiple citation formats to the same document
                citation_map[citation_filename] = doc_info
                citation_map[citation_filename.replace('.pdf', '')] = doc_info
                citation_map[display_title] = doc_info
                
                # Also map variations without special characters
                clean_title = display_title.replace('•', '').replace('  ', ' ').strip()
                citation_map[clean_title] = doc_info

    # Enhanced pattern to match any bracketed citation (not just .pdf)
    citation_pattern = r'\[([^\]]+)\]'

    def replace_citation(match):
        citation_text = match.group(1)
        
        # Try exact match first
        if citation_text in citation_map:
            doc_info = citation_map[citation_text]
            if doc_info['type'] == 'pdf':
                href = f"/download_gated/{doc_info['source']}"
                display_text = citation_text  # Use the original citation text as display
                return f"<a href='{href}' target='_blank' rel='noopener noreferrer' class='text-blue-600 hover:underline'>{display_text}</a>"
        
        # Try fuzzy matching for slight variations
        for mapped_citation, doc_info in citation_map.items():
            if (citation_text.lower() in mapped_citation.lower() or 
                mapped_citation.lower() in citation_text.lower()) and len(citation_text) > 5:
                if doc_info['type'] == 'pdf':
                    href = f"/download_gated/{doc_info['source']}"
                    display_text = citation_text  # Use the original citation text as display
                    return f"<a href='{href}' target='_blank' rel='noopener noreferrer' class='text-blue-600 hover:underline'>{display_text}</a>"

        # If no mapping found, return original citation
        return match.group(0)

    # Replace all citation placeholders
    processed_answer = re.sub(citation_pattern, replace_citation, answer)

    # Handle specific patterns like "According to [citation]", "As mentioned in [citation]", etc.
    context_patterns = [
        r'According to \[([^\]]+)\]',
        r'As mentioned in \[([^\]]+)\]',
        r'As stated in \[([^\]]+)\]',
        r'Based on \[([^\]]+)\]',
        r'From \[([^\]]+)\]'
    ]

    def replace_context_citation(match):
        citation_text = match.group(1)
        prefix = match.group(0).split('[')[0]  # Get "According to ", "As mentioned in ", etc.
        
        # Try exact match first
        if citation_text in citation_map:
            doc_info = citation_map[citation_text]
            if doc_info['type'] == 'pdf':
                href = f"/download_gated/{doc_info['source']}"
                display_text = citation_text
                return f"{prefix}<a href='{href}' target='_blank' rel='noopener noreferrer' class='text-blue-600 hover:underline'>{display_text}</a>"
        
        # Try fuzzy matching
        for mapped_citation, doc_info in citation_map.items():
            if (citation_text.lower() in mapped_citation.lower() or 
                mapped_citation.lower() in citation_text.lower()) and len(citation_text) > 5:
                if doc_info['type'] == 'pdf':
                    href = f"/download_gated/{doc_info['source']}"
                    display_text = citation_text
                    return f"{prefix}<a href='{href}' target='_blank' rel='noopener noreferrer' class='text-blue-600 hover:underline'>{display_text}</a>"

        return match.group(0)

    # Apply context pattern replacements
    for pattern in context_patterns:
        processed_answer = re.sub(pattern, replace_context_citation, processed_answer)

    return processed_answer

def detect_hallucinations(answer: str, docs: List[Document], anti_hallucination_mode: str, config: QueryConfiguration) -> Dict[str, Any]:
    """
    Detect potential hallucinations in the AI response.

    Args:
        answer: The AI-generated answer
        docs: List of context documents
        anti_hallucination_mode: Current anti-hallucination mode
        config: Query configuration with hallucination thresholds

    Returns:
        Dictionary with detection results
    """
    if not config.enable_hallucination_detection or anti_hallucination_mode == 'off':
        return {'detected': False, 'warning': None}

    # Load insufficient info phrases from config
    try:
        if os.path.exists(DEFAULT_MODELS_FILE):
            with open(DEFAULT_MODELS_FILE, 'r') as f:
                defaults = json.load(f)
            insufficient_phrases = defaults.get('query_parameters', {}).get('insufficient_info_phrases', [])
        else:
            insufficient_phrases = []
    except Exception:
        insufficient_phrases = []

    # Default phrases if none in config
    if not insufficient_phrases:
        insufficient_phrases = [
            "I don't have enough information",
            "The provided context does not contain",
            "There is no information",
            "The context doesn't mention",
            "I cannot find information"
        ]

    # Check if answer indicates insufficient information
    has_insufficient_info = any(phrase.lower() in answer.lower() for phrase in insufficient_phrases)

    if has_insufficient_info:
        return {'detected': False, 'warning': None}

    # Extract statements from the answer for verification
    statements = []
    for line in answer.split('\n'):
        # Skip citation lines
        if 'According to [' in line or '<a href=' in line:
            continue

        # Skip inference lines in balanced mode
        if anti_hallucination_mode == 'balanced' and any(phrase in line for phrase in [
            "Based on the context, it seems",
            "The information suggests",
            "It appears that",
            "This suggests",
            "It is likely",
            "We can infer"
        ]):
            continue

        # Process bullet points and sentences
        if line.strip().startswith(('*', '-', '•')):
            statements.append(line.strip())
        else:
            # Split by periods for sentences
            sentences = [s.strip() for s in line.split('.') if s.strip()]
            statements.extend(sentences)

    # Check each statement against context
    unsupported_statements = []
    threshold = config.get_hallucination_threshold(anti_hallucination_mode)

    for statement in statements:
        if len(statement) < config.min_statement_length:
            continue

        statement_words = set(statement.lower().split())
        supported = False

        for doc in docs:
            content_words = set(doc.page_content.lower().split())
            overlap = statement_words.intersection(content_words)

            if len(statement_words) > 0 and len(overlap) / len(statement_words) > threshold:
                supported = True
                break

        if not supported:
            unsupported_statements.append(statement)

    if unsupported_statements:
        warning = f"Some statements may not be fully supported by the provided context (mode: {anti_hallucination_mode})"
        return {
            'detected': True,
            'warning': warning,
            'unsupported_count': len(unsupported_statements),
            'total_statements': len(statements)
        }

    return {'detected': False, 'warning': None}

def score_document_relevance(doc: Document, question: str) -> float:
    """
    Score the relevance of a document to a question.
    Higher score means more relevant.

    Args:
        doc: The document to score
        question: The question to compare against

    Returns:
        float: A relevance score between 0 and 1
    """
    # Simple keyword matching for now
    # Count how many words from the question appear in the document
    question_words = set(question.lower().split())
    # Remove common stop words
    stop_words = {'the', 'a', 'an', 'in', 'on', 'at', 'to', 'for', 'with', 'by', 'about', 'like', 'through', 'over', 'before', 'between', 'after', 'since', 'without', 'under', 'within', 'along', 'following', 'across', 'behind', 'beyond', 'plus', 'except', 'but', 'up', 'out', 'around', 'down', 'off', 'above', 'near', 'and', 'or', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did', 'can', 'could', 'will', 'would', 'shall', 'should', 'may', 'might', 'must', 'of'}
    question_words = question_words - stop_words

    content_lower = doc.page_content.lower()

    # Count occurrences of question words in the document
    word_matches = sum(1 for word in question_words if word in content_lower)

    # Calculate basic relevance score
    if len(question_words) > 0:
        basic_score = word_matches / len(question_words)
    else:
        basic_score = 0

    # Boost score if document contains exact phrases from the question
    # This helps with multi-word concepts
    phrase_boost = 0
    for i in range(2, min(5, len(question.split()) + 1)):  # Check phrases of length 2-4 words
        for j in range(len(question.split()) - i + 1):
            phrase = ' '.join(question.split()[j:j+i]).lower()
            if len(phrase) > 3 and phrase in content_lower:  # Only consider phrases longer than 3 chars
                phrase_boost += 0.1 * i  # Longer matching phrases get higher boost

    # Calculate final score, capped at 1.0
    final_score = min(1.0, basic_score + phrase_boost)

    # Log the score for debugging
    logger.debug(f"Document relevance score: {final_score:.2f} for doc: {doc.metadata.get('source', 'Unknown')[:30]}...")

    return final_score

def get_adaptive_k(query: str, base_k: int = 12) -> int:
    """
    Dynamically adjust retrieval_k based on query complexity.

    Args:
        query: The user's query text
        base_k: Base retrieval value from configuration

    Returns:
        Adjusted k value based on query complexity
    """
    word_count = len(query.split())

    if word_count <= 3:
        # Simple queries - fewer documents needed
        adaptive_k = max(6, int(base_k * 0.67))  # ~8 for base_k=12
        logger.debug(f"Simple query ({word_count} words): k={adaptive_k}")
    elif word_count <= 10:
        # Medium complexity - use base value
        adaptive_k = base_k
        logger.debug(f"Medium query ({word_count} words): k={adaptive_k}")
    else:
        # Complex queries - more documents for better context
        adaptive_k = min(20, int(base_k * 1.33))  # ~16 for base_k=12
        logger.debug(f"Complex query ({word_count} words): k={adaptive_k}")

    return adaptive_k

def filter_relevant_documents(docs: List[Document], question: str, threshold: float = 0.2, use_parallel: bool = True) -> List[Tuple[Document, float]]:
    """
    Filter and sort documents by relevance to the question with optional parallel processing.

    Args:
        docs: List of documents to filter
        question: The question to compare against
        threshold: Minimum relevance score to keep a document
        use_parallel: Whether to use parallel processing for scoring

    Returns:
        List of (document, score) tuples, sorted by relevance (highest first)
    """
    if use_parallel and len(docs) > 4:  # Only use parallel processing for larger document sets
        # Parallel scoring with ThreadPoolExecutor
        scored_docs = []
        max_workers = min(4, len(docs))  # Limit to 4 workers as specified

        # Record parallel processing start time
        parallel_start_time = time.time()

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all scoring tasks
            future_to_doc = {
                executor.submit(score_document_relevance, doc, question): doc
                for doc in docs
            }

            # Collect results as they complete
            for future in as_completed(future_to_doc):
                doc = future_to_doc[future]
                try:
                    score = future.result()
                    scored_docs.append((doc, score))
                except Exception as e:
                    logger.error(f"Error scoring document {doc.metadata.get('source', 'Unknown')}: {e}")
                    # Assign a low score for failed documents
                    scored_docs.append((doc, 0.0))

        # Record parallel processing metrics
        parallel_execution_time = time.time() - parallel_start_time
        rag_monitor = get_rag_monitor()
        rag_monitor.record_parallel_scoring(
            execution_time=parallel_execution_time,
            workers=max_workers,
            document_count=len(docs),
            category="unknown"  # Category will be passed from calling function
        )

        logger.info(f"Parallel document scoring completed with {max_workers} workers in {parallel_execution_time:.3f}s")
    else:
        # Sequential scoring for smaller document sets or when parallel is disabled
        scored_docs = [(doc, score_document_relevance(doc, question)) for doc in docs]
        logger.info("Sequential document scoring completed")

    # Filter by threshold and sort by score (descending)
    relevant_docs = [(doc, score) for doc, score in scored_docs if score >= threshold]
    relevant_docs.sort(key=lambda x: x[1], reverse=True)

    # Log the filtering results
    logger.info(f"Filtered documents: {len(relevant_docs)}/{len(docs)} passed threshold {threshold}")
    for i, (doc, score) in enumerate(relevant_docs[:5]):  # Log top 5 for debugging
        logger.info(f"Top doc {i+1}: score={score:.2f}, source={doc.metadata.get('source', 'Unknown')}")

    return relevant_docs

def extract_images_and_links(text, base_url=None):
    """Extract image URLs and download links from text content."""
    images = []
    pdf_links = []

    # Extract JPG image URLs
    jpg_img_pattern = re.compile(r'https?://[^\s<>"]+\.(?:jpg|jpeg|JPG|JPEG)')
    jpg_img_matches = jpg_img_pattern.findall(text)

    # Extract PNG image URLs (excluding those with "logo" in the filename)
    png_img_pattern = re.compile(r'https?://[^\s<>"]+\.(?:png|PNG)')
    png_img_matches = [url for url in png_img_pattern.findall(text) if 'logo' not in url.lower()]

    # Combine JPG and filtered PNG images
    priority_img_matches = jpg_img_matches + png_img_matches

    # Extract other image URLs as backup
    other_img_pattern = re.compile(r'https?://[^\s<>"]+\.(?:gif|bmp|GIF|BMP)')
    other_img_matches = other_img_pattern.findall(text)

    # Combine with priority images first
    img_matches = priority_img_matches + other_img_matches

    # Extract PDF/document download links
    pdf_pattern = re.compile(r'https?://[^\s<>"]+\.(?:pdf|doc|docx|xls|xlsx|ppt|pptx|PDF|DOC|DOCX|XLS|XLSX|PPT|PPTX)')
    pdf_matches = pdf_pattern.findall(text)

    # Extract links with "download" or "request" in them
    download_pattern = re.compile(r'https?://[^\s<>"]+(?:download|request|copy)[^\s<>"]*')
    download_matches = download_pattern.findall(text)

    # Combine and deduplicate
    images = list(set(img_matches))
    pdf_links = list(set(pdf_matches + download_matches))

    # If base_url is provided, try to scrape the page for additional images and links
    if base_url and base_url.startswith('http'):
        try:
            response = requests.get(base_url, timeout=10, verify=False)
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')

                # Find images - prioritize JPG and PNG images
                priority_images = []
                other_images = []
                for img in soup.find_all('img', src=True):
                    img_src = img['src'] if img and 'src' in img.attrs else ''
                    if not img_src:
                        continue
                    
                    img_url = str(img_src)
                    if not img_url.startswith(('http://', 'https://')):
                        img_url = urljoin(base_url, img_url)

                    # Separate JPG/PNG images from other formats
                    if img_url.lower().endswith(('.jpg', '.jpeg')):
                        priority_images.append(img_url)
                    elif img_url.lower().endswith('.png'):
                        # Skip PNG files with "logo" in the filename (case insensitive)
                        if 'logo' not in img_url.lower():
                            priority_images.append(img_url)
                    elif img_url.lower().endswith(('.gif', '.bmp')):
                        other_images.append(img_url)

                # Add priority images first, then others if needed
                images.extend(priority_images)
                images.extend(other_images)

                # Find download links
                for a in soup.find_all('a', href=True):
                    href = a['href'] if a and 'href' in a.attrs else ''
                    if not href or not isinstance(href, str):
                        continue
                    if not href.startswith(('http://', 'https://')):
                        href = urljoin(base_url, href)
                    if href.endswith(('.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx')):
                        pdf_links.append(href)
                    elif any(keyword in href.lower() for keyword in ['download', 'request', 'copy']):
                        pdf_links.append(href)
        except Exception as e:
            logger.warning(f"Failed to scrape additional content from {base_url}: {str(e)}")

    # Deduplicate again
    images = list(set(images))
    pdf_links = list(set(pdf_links))

    return images, pdf_links

@performance_monitor(track_memory=True, track_cpu=True, log_parameters=True, log_result_size=True)
def query_category(category: str, question: str, anti_hallucination_mode: str = 'strict', client_name: str = None, session_id: str = None, device_fingerprint: str = None, selected_model: str = None):
    try:
        # Validate category parameter
        if not category or not category.strip():
            raise ValueError("Category parameter is required and cannot be empty")

        # Determine the model name for caching
        model_name = selected_model if selected_model else os.getenv('LLM_MODEL', 'llama3.1:8b-instruct-q4_K_M')

        # Check cache first
        cached_result = QueryCache.get_cached_query_result(category, question, anti_hallucination_mode, model_name)
        if cached_result is not None:
            logger.info(f"Cache hit for query in category: {category}")
            # Record cache hit in RAG performance monitor
            rag_monitor = get_rag_monitor()
            rag_monitor.record_cache_hit('query')

            # Add cache metadata to the result
            cached_result['metadata'] = cached_result.get('metadata', {})
            cached_result['metadata']['cached'] = True
            cached_result['metadata']['cache_timestamp'] = datetime.now().isoformat()
            return cached_result
        else:
            # Record cache miss
            rag_monitor = get_rag_monitor()
            rag_monitor.record_cache_miss('query')
        
        category = category.strip()
        logger.info(f"Processing query for category: '{category}'")
        
        # Check if this is a parent category and get child categories
        child_categories = []
        is_parent_category = False
        category_exists_as_child = False
        
        try:
            from app.utils.helpers import get_categories_by_parent, get_parent_category_info, list_categories
            
            # First check if this is a parent category
            parent_info = get_parent_category_info(category)
            child_categories = get_categories_by_parent(category)
            
            if parent_info or child_categories:
                # This is a parent category
                is_parent_category = True
                logger.info(f"Detected parent category '{category}' with child categories: {child_categories}")
            else:
                # Check if this is a child category (leaf category)
                from app.utils.helpers import list_upload_categories
                available_child_categories = list_upload_categories()
                category_exists_as_child = category in available_child_categories
                
                if category_exists_as_child:
                    logger.info(f"Category '{category}' exists as child category, treating as direct category")
                else:
                    logger.warning(f"Category '{category}' not found as parent or child category")
        except Exception as e:
            logger.warning(f"Could not check for parent category: {str(e)}")
        
        # Additional logging for category validation
        if not category_exists_as_child and not is_parent_category:
            logger.warning(f"Category '{category}' not found in available categories or parent categories")
            # Don't raise an error here, just log a warning as the category might exist in the vector DB
        
        # Start timing for analytics
        start_time = time.time()

        # Load query configuration
        config = get_query_config()
        logger.info(f"Loaded query configuration: retrieval_k={config.retrieval_k}, relevance_threshold={config.relevance_threshold}")

        # Initialize LLM with the selected model from request or environment variable
        model_name = selected_model if selected_model else os.getenv('LLM_MODEL', 'llama3.1:8b-instruct-q4_K_M')
        embedding_model = os.getenv('TEXT_EMBEDDING_MODEL', 'mxbai-embed-large:latest')

        logger.info(f"Using LLM model: {model_name} (selected: {selected_model is not None})")

        # Load mode-specific model parameters from config file
        if os.path.exists(DEFAULT_MODELS_FILE):
            with open(DEFAULT_MODELS_FILE, 'r') as f:
                defaults = json.load(f)
            model_params_all = defaults.get('model_parameters', {})
            # Fallback to 'balanced' if mode not found
            mode_key = anti_hallucination_mode if anti_hallucination_mode in model_params_all else 'balanced'
            model_params = model_params_all.get(mode_key, model_params_all.get('balanced', {}))
        else:
            model_params = {}

        temperature = float(model_params.get('temperature', 0.7))
        num_ctx = int(model_params.get('num_ctx', 4096))
        num_predict = int(model_params.get('num_predict', 256))
        top_p = float(model_params.get('top_p', 0.9))
        top_k = int(model_params.get('top_k', 40))
        repeat_penalty = float(model_params.get('repeat_penalty', 1.1))
        system_prompt = model_params.get('system_prompt', 'You are a helpful assistant for the ERDB (Ecosystems Research and Development Bureau). Answer questions based on the provided context.')

        logger.info(f"Using LLM model: {model_name}, Embedding model: {embedding_model}, Anti-hallucination mode: {anti_hallucination_mode}")
        logger.info(f"Model parameters: temperature={temperature}, num_ctx={num_ctx}, num_predict={num_predict}, top_p={top_p}, top_k={top_k}, repeat_penalty={repeat_penalty}")

        try:
            # Initialize ChatOllama with model parameters
            llm = ChatOllama(
                model=model_name,
                temperature=temperature,
                num_ctx=num_ctx,
                num_predict=num_predict,
                top_p=top_p,
                top_k=top_k,
                repeat_penalty=repeat_penalty,
                system=system_prompt
            )
        except Exception as e:
            logger.error(f"Failed to initialize ChatOllama with model {model_name}: {str(e)}")
            # Fallback to default model
            llm = ChatOllama(model=os.getenv('LLM_MODEL', 'llama3.1:8b-instruct-q4_K_M'))

        # Initialize embedding model
        try:
            embeddings = OllamaEmbeddings(model=embedding_model)
        except Exception as e:
            logger.error(f"Failed to initialize OllamaEmbeddings with model {embedding_model}: {str(e)}")
            # Fallback to default embedding model
            embeddings = OllamaEmbeddings(model=os.getenv('TEXT_EMBEDDING_MODEL', 'mxbai-embed-large:latest'))

        # Initialize vector store
        try:
            vectorstore = Chroma(
                persist_directory=CHROMA_PATH,
                embedding_function=embeddings,
                collection_name="unified_collection"
            )
        except Exception as e:
            logger.error(f"Failed to initialize Chroma vectorstore: {str(e)}")
            raise Exception(f"Vector store initialization failed: {str(e)}")

        # Determine which categories to search
        categories_to_search = []
        if is_parent_category and child_categories:
            # Search across all child categories of the parent
            categories_to_search = child_categories
            logger.info(f"Parent category query: searching across child categories: {categories_to_search}")
        elif is_parent_category and not child_categories:
            # Parent category with no children - search the parent category itself
            categories_to_search = [category]
            logger.info(f"Parent category with no children: searching in category: {category}")
        else:
            # Search in the specific child category
            categories_to_search = [category]
            logger.info(f"Child category query: searching in specific category: {category}")

        # If no categories to search, return an error
        if not categories_to_search:
            return {
                'answer': f"No content found for category '{category}'. Please check if the category exists and has content.",
                'sources': [],
                'metadata': {
                    'category': category,
                    'is_parent_category': is_parent_category,
                    'child_categories': child_categories,
                    'search_categories': categories_to_search
                }
            }

        # Search across all relevant categories
        all_docs = []
        for search_category in categories_to_search:
            try:
                # Search in the specific category
                docs = vectorstore.similarity_search_with_relevance_scores(
                    question,
                    k=config.retrieval_k,
                    filter={"category": search_category}
                )
                all_docs.extend(docs)
                logger.info(f"Found {len(docs)} documents in category '{search_category}'")
            except Exception as e:
                logger.warning(f"Error searching in category '{search_category}': {str(e)}")
                continue

        # If no documents found, try fallback strategies
        if not all_docs:
            logger.warning(f"No documents found in primary search for category '{category}'")
            
            # Fallback 1: If we were searching child categories, try the parent category directly
            if is_parent_category and child_categories:
                logger.info(f"Trying fallback search for parent category '{category}' directly")
                try:
                    fallback_docs = vectorstore.similarity_search_with_relevance_scores(
                        question,
                        k=config.retrieval_k,
                        filter={"category": category}
                    )
                    if fallback_docs:
                        all_docs = fallback_docs
                        logger.info(f"Fallback search found {len(fallback_docs)} documents")
                except Exception as e:
                    logger.warning(f"Fallback search failed: {str(e)}")
            
            # Fallback 2: Try search without category filter
            if not all_docs:
                logger.info("Trying search without category filter as final fallback")
                try:
                    fallback_docs = vectorstore.similarity_search_with_relevance_scores(
                        question,
                        k=config.retrieval_k
                    )
                    if fallback_docs:
                        # Filter for documents that might be related to the category
                        relevant_fallback = []
                        for doc, score in fallback_docs:
                            doc_category = doc.metadata.get('category', '').lower()
                            if category.lower() in doc_category or doc_category in category.lower():
                                relevant_fallback.append((doc, score))
                        
                        if relevant_fallback:
                            all_docs = relevant_fallback
                            logger.info(f"Final fallback found {len(relevant_fallback)} relevant documents")
                except Exception as e:
                    logger.warning(f"Final fallback search failed: {str(e)}")
            
            # If still no documents found, return error
            if not all_docs:
                return {
                    'answer': f"No relevant content found for your question in category '{category}'. Please try rephrasing your question or check if the category has content.",
                    'sources': [],
                    'metadata': {
                        'category': category,
                        'is_parent_category': is_parent_category,
                        'child_categories': child_categories,
                        'search_categories': categories_to_search,
                        'fallback_attempted': True
                    }
                }

        # Sort documents by relevance score and take the top ones
        all_docs.sort(key=lambda x: x[1], reverse=True)
        top_docs = all_docs[:config.retrieval_k]

        # Extract just the documents (without scores) for processing
        docs = [doc for doc, score in top_docs]
        relevance_scores = [score for doc, score in top_docs]

        logger.info(f"Retrieved {len(docs)} documents with relevance scores: {relevance_scores[:5]}...")

        # Filter documents based on relevance threshold
        filtered_docs = []
        for doc, score in zip(docs, relevance_scores):
            if score >= config.relevance_threshold:
                filtered_docs.append(doc)
            else:
                logger.debug(f"Filtered out document with low relevance score: {score}")

        if not filtered_docs:
            logger.warning(f"No documents met the relevance threshold ({config.relevance_threshold})")
            # Use the top documents even if they don't meet the threshold
            filtered_docs = docs[:5]

        logger.info(f"Using {len(filtered_docs)} documents for answer generation")

        # Prepare context from documents
        context = "\n\n".join([doc.page_content for doc in filtered_docs])

        # Load prompt templates from config
        prompt_template = None
        try:
            if os.path.exists(DEFAULT_MODELS_FILE):
                with open(DEFAULT_MODELS_FILE, 'r') as f:
                    defaults = json.load(f)
                prompt_templates = defaults.get('query_parameters', {}).get('prompt_templates', {})

                # Select appropriate template based on anti-hallucination mode
                if anti_hallucination_mode in prompt_templates:
                    prompt_template = prompt_templates[anti_hallucination_mode]
                    logger.info(f"Using {anti_hallucination_mode} mode prompt template")
                else:
                    # Fallback to balanced mode
                    prompt_template = prompt_templates.get('balanced', prompt_templates.get('general', None))
                    logger.warning(f"Mode {anti_hallucination_mode} not found, using fallback template")
        except Exception as e:
            logger.error(f"Error loading prompt templates: {str(e)}")

        # Create the prompt with context
        if prompt_template:
            # Use the sophisticated template from config
            prompt = prompt_template.format(context=context, question=question)
        else:
            # Fallback to basic prompt if templates not available
            logger.warning("Using fallback basic prompt - templates not available")
            prompt = f"""Based on the following context, answer the question. If the answer cannot be found in the context, say "I don't have enough information to answer this question."

Context:
{context}

Question: {question}

Answer:"""

        # Generate answer
        try:
            response = llm.invoke(prompt)
            answer = response.content if hasattr(response, 'content') else str(response)
        except Exception as e:
            logger.error(f"Error generating answer: {str(e)}")
            answer = f"Error generating answer: {str(e)}"

        # Post-processing: Convert citation placeholders to HTML links
        logger.info(f"Processing citations for {len(filtered_docs)} documents")
        answer_before_citations = answer
        answer = process_citations(answer, filtered_docs)
        if answer != answer_before_citations:
            logger.info("Citations were processed and converted to HTML links")
        else:
            logger.info("No citation patterns found to convert")
        
        # Fallback citation processing if bracket patterns or bare filenames remain
        bracket_patterns = re.findall(r'\[([^\]]+\.pdf)\]', answer, re.IGNORECASE)
        bare_filenames = re.findall(r'\b([a-zA-Z0-9_\-\.]+\.pdf)\b', answer, re.IGNORECASE)
        # Filter out bare filenames that are already in links
        bare_filenames = [f for f in bare_filenames if f'>{f}<' not in answer and f'href=' not in answer[max(0, answer.find(f)-50):answer.find(f)+50]]
        
        if bracket_patterns or bare_filenames:
            logger.info(f"Found unconverted citations: brackets={bracket_patterns}, bare_filenames={bare_filenames}")
            answer = convert_citation_placeholders(answer, filtered_docs)
            logger.info("Applied fallback citation conversion")

        # Final safeguard: remove any leftover bracket citations that could not be mapped
        # and rewrite any accidental direct file links to gated downloads
        try:
            # Build allowed citation targets from docs (download_gated paths)
            allowed_paths = set()
            allowed_filenames = set()
            for doc in filtered_docs:
                if hasattr(doc, 'metadata') and doc.metadata.get('type') == 'pdf':
                    source_name = doc.metadata.get('source') or doc.metadata.get('filename')
                    citation_filename = doc.metadata.get('citation_filename', doc.metadata.get('original_filename', ''))
                    if source_name:
                        allowed_paths.add(f"/download_gated/{source_name}")
                    if citation_filename:
                        allowed_filenames.add(citation_filename.lower())
                        allowed_filenames.add(citation_filename.replace('.pdf','').lower())

            # Rewrite any direct /data/temp links to /download_gated if we can infer the source
            answer = re.sub(r"/data/temp/[^\s'\"]+/(\d{8,}_[^\s'\"]+\.pdf)", r"/download_gated/\\1", answer)

            # Strip any [filename.pdf] that isn't in allowed_filenames
            def _strip_unmatched_brackets(m):
                fname = m.group(1)
                key = fname.lower()
                key2 = key.replace('.pdf','')
                if key in allowed_filenames or key2 in allowed_filenames:
                    return m.group(0)  # keep
                # remove brackets, leave plain text to avoid fake links
                return fname

            answer = re.sub(r"\[([^\]]+\.pdf)\]", _strip_unmatched_brackets, answer, flags=re.IGNORECASE)
        except Exception as _safeguard_err:
            logger.warning(f"Citation/link safeguard step skipped due to error: {_safeguard_err}")

        # Post-processing: Detect potential hallucinations
        hallucination_result = detect_hallucinations(answer, filtered_docs, anti_hallucination_mode, config)
        if hallucination_result.get('detected'):
            logger.warning(f"Potential hallucination detected: {hallucination_result.get('warning', '')}")

        # Extract sources from documents
        sources = []
        for doc in filtered_docs:
            if hasattr(doc, 'metadata'):
                filename = doc.metadata.get('filename', 'Unknown')
                source_name = doc.metadata.get('source', filename)
                display_name = doc.metadata.get('citation_filename', doc.metadata.get('original_filename', filename))
                doc_type = doc.metadata.get('type', 'pdf')
                
                # Ensure we have proper source name for gated downloads
                if doc_type == 'pdf' and not source_name:
                    source_name = filename
                
                source_info = {
                    'title': doc.metadata.get('title', 'Unknown'),
                    'category': doc.metadata.get('category', 'Unknown'),
                    'filename': filename,
                    'page': doc.metadata.get('page', 1),
                    'display_name': display_name,  # Clean display name for frontend
                    'source': source_name,  # Source field for compatibility
                    'type': doc_type,  # Type field for frontend
                    'file_path': f"/download_gated/{source_name}" if doc_type == 'pdf' else None
                }
                sources.append(source_info)
                logger.debug(f"Added source: {display_name} -> /download_gated/{source_name}")

        # Generate document thumbnails for frontend display with deduplication
        document_thumbnails = []
        seen_urls = set()
        cover_images = []
        other_thumbnails = []
        
        for doc in filtered_docs:
            if hasattr(doc, 'metadata') and doc.metadata.get('type') == 'pdf':
                # Get thumbnail URL from metadata or derive it
                thumbnail_url = doc.metadata.get('thumbnail_url', '')
                
                # If no thumbnail_url in metadata, try to derive it
                if not thumbnail_url:
                    source_name = doc.metadata.get('source', '')
                    doc_category = doc.metadata.get('category', category)
                    if source_name and doc_category:
                        # Remove .pdf extension and timestamp prefix for base name
                        base_name = source_name.replace('.pdf', '')
                        if '_' in base_name:
                            base_name = base_name.split('_', 1)[1]  # Remove timestamp prefix
                        thumbnail_url = f"/{doc_category}/{base_name}/pdf_images/cover_image/{base_name}_thumbnail.jpg"
                        logger.info(f"Derived thumbnail URL for {source_name}: {thumbnail_url}")
                
                # Deduplicate by URL
                if thumbnail_url and thumbnail_url not in seen_urls:
                    seen_urls.add(thumbnail_url)
                    display_name = doc.metadata.get('citation_filename', doc.metadata.get('original_filename', 'Unknown'))
                    is_cover_image = "/cover_image/" in thumbnail_url
                    cover_class = "cover-image-thumbnail" if is_cover_image else "document-thumbnail"
                    
                    thumbnail_html = f'''
                    <div class="image-container {'cover-image' if is_cover_image else ''}" style="display: inline-block; margin: 0.25rem;">
                        <img src="{thumbnail_url}" 
                             alt="Thumbnail for {display_name}" 
                             class="{cover_class}"
                             title="{display_name}"
                             onerror="this.style.display='none';" />
                    </div>'''
                    
                    if is_cover_image:
                        cover_images.append(thumbnail_html)
                    else:
                        other_thumbnails.append(thumbnail_html)
        
        # Combine cover images first, then other thumbnails (deduplicated)
        document_thumbnails = cover_images + other_thumbnails
        logger.info(f"Generated {len(document_thumbnails)} unique thumbnails ({len(cover_images)} cover images, {len(other_thumbnails)} other)")

        # Calculate processing time
        processing_time = time.time() - start_time

        # Prepare result
        result = {
            'answer': answer,
            'sources': sources,
            'document_thumbnails': document_thumbnails,  # Add thumbnails for frontend
            'metadata': {
                'category': category,
                'is_parent_category': is_parent_category,
                'child_categories': child_categories,
                'search_categories': categories_to_search,
                'processing_time': processing_time,
                'documents_retrieved': len(docs),
                'documents_used': len(filtered_docs),
                'relevance_scores': relevance_scores[:5],  # Top 5 scores
                'anti_hallucination_mode': anti_hallucination_mode,
                'hallucination_check': hallucination_result,
                'prompt_template_used': prompt_template is not None
            }
        }

        # Cache the result
        QueryCache.cache_query_result(category, question, anti_hallucination_mode, model_name, result)

        return result

    except Exception as e:
        logger.error(f"Error in query_category: {str(e)}")
        return {
            'answer': f"An error occurred while processing your query: {str(e)}",
            'sources': [],
            'metadata': {
                'error': str(e),
                'category': category
            }
        }

def enrich_document_metadata(docs: List[Document]) -> List[Document]:
    """
    Enrich document metadata by resolving source_url_id to actual URLs.
    
    Args:
        docs: List of documents with metadata
        
    Returns:
        List of documents with enriched metadata
    """
    for doc in docs:
        # Check if document has source_url_id but no original_url
        source_url_id = doc.metadata.get("source_url_id")
        if source_url_id and not doc.metadata.get("original_url"):
            try:
                # Resolve the source URL ID to actual URL
                actual_url = get_source_url_by_id(source_url_id)
                if actual_url:
                    doc.metadata["original_url"] = actual_url
                    logger.info(f"Resolved source_url_id {source_url_id} to URL: {actual_url}")
                else:
                    logger.warning(f"Could not resolve source_url_id {source_url_id} to URL")
            except Exception as e:
                logger.error(f"Error resolving source_url_id {source_url_id}: {str(e)}")
    
    return docs

def fix_generic_document_references(text: str, docs: List[Document]) -> str:
    """
    Fix generic document references like "Document 5", "Document 8", "Source 1", etc.
    by replacing them with actual citation filenames.
    
    Args:
        text: The text containing generic document references
        docs: List of documents to map generic references to actual filenames
        
    Returns:
        Text with generic references replaced by actual filenames
    """
    logger.info(f"[Generic Reference Fix] Starting generic reference detection and replacement")
    
    # Build mapping from document index to citation filename
    doc_mapping = {}
    for i, doc in enumerate(docs):
        if doc.metadata.get("type") == "pdf":
            citation_filename = doc.metadata.get("citation_filename", doc.metadata.get("original_filename", ""))
            if citation_filename:
                doc_mapping[i + 1] = citation_filename  # Map "Document 1" to first document, etc.
                logger.info(f"[Generic Reference Fix] Mapped Document {i+1} -> {citation_filename}")
    
    if not doc_mapping:
        logger.warning("[Generic Reference Fix] No document mapping created")
        return text
    
    # Patterns to match generic document references
    patterns = [
        (r'Document\s+(\d+)', 'Document {}'),  # "Document 5" -> "Document 5"
        (r'Source\s+(\d+)', 'Source {}'),      # "Source 1" -> "Source 1"
        (r'Doc\s+(\d+)', 'Doc {}'),           # "Doc 3" -> "Doc 3"
    ]
    
    replacements_made = 0
    
    for pattern, template in patterns:
        def replace_generic_ref(match):
            nonlocal replacements_made
            doc_num = int(match.group(1))
            if doc_num in doc_mapping:
                actual_filename = doc_mapping[doc_num]
                replacements_made += 1
                logger.info(f"[Generic Reference Fix] Replaced '{match.group(0)}' with '{actual_filename}'")
                return actual_filename
            else:
                logger.warning(f"[Generic Reference Fix] No mapping found for {match.group(0)}")
                return match.group(0)  # Keep original if no mapping
        
        text = re.sub(pattern, replace_generic_ref, text, flags=re.IGNORECASE)
    
    if replacements_made > 0:
        logger.info(f"[Generic Reference Fix] Made {replacements_made} replacements")
    else:
        logger.info("[Generic Reference Fix] No generic references found")
    
    return text

def convert_citation_placeholders(text: str, docs: List[Document]) -> str:
    """
    Convert simple citation placeholders to proper HTML citations.
    
    Converts patterns like:
    - "According to [canopy_vol45n1.pdf]" 
    - "As stated in [filename.pdf]"
    - "[filename.pdf] shows that..."
    - "According to filename.pdf" (without brackets)
    - "The canopy_vol45n1.pdf document shows" (direct filename references)
    
    To proper HTML citations with correct file paths.
    
    Args:
        text: The text containing citation placeholders
        docs: List of documents to build citation mappings from
        
    Returns:
        Text with placeholders converted to HTML citations
    """
    logger.info(f"[Citation Conversion] Starting placeholder conversion")
    logger.info(f"[Citation Conversion] Input text preview: {text[:200]}...")
    logger.info(f"[Citation Conversion] Processing {len(docs)} documents")

    # Build citation mapping from document metadata
    citation_map = {}

    for i, doc in enumerate(docs):
        logger.info(f"[Citation Conversion] Document {i+1}: type={doc.metadata.get('type')}, source={doc.metadata.get('source', 'N/A')}")

        if doc.metadata.get("type") == "pdf":
            source_name = doc.metadata.get("source", "")
            citation_filename = doc.metadata.get("citation_filename", doc.metadata.get("original_filename", ""))
            page_num = doc.metadata.get("page", None)

            logger.info(f"[Citation Conversion] PDF Document {i+1}:")
            logger.info(f"  - source_name: {source_name}")
            logger.info(f"  - citation_filename: {citation_filename}")
            logger.info(f"  - page_num: {page_num}")

            # Generate the correct file path using the same logic as sources section
            correct_file_path = f"/download_gated/{source_name}"

            # Map citation filename to proper HTML citation
            if citation_filename:
                page_text = f" (Page {page_num})" if page_num else ""
                html_citation = f'<a href="{correct_file_path}" target="_blank" rel="noopener noreferrer" class="text-blue-600 hover:underline">{citation_filename}{page_text}</a>'

                # Create multiple mapping entries for different possible formats
                citation_map[citation_filename.lower()] = html_citation

                # Also map without .pdf extension
                filename_no_ext = citation_filename.replace('.pdf', '').lower()
                citation_map[filename_no_ext] = html_citation

                # Map the source name as well (timestamped filename)
                if source_name:
                    citation_map[source_name.lower()] = html_citation
                    source_no_ext = source_name.replace('.pdf', '').lower()
                    citation_map[source_no_ext] = html_citation

                logger.info(f"[Citation Conversion] Mapped '{citation_filename}' -> '{correct_file_path}'")
                logger.info(f"[Citation Conversion] Added mapping keys: {[citation_filename.lower(), filename_no_ext, source_name.lower(), source_no_ext]}")
            else:
                logger.warning(f"[Citation Conversion] No citation_filename found for document {i+1}")

    logger.info(f"[Citation Conversion] Built citation map with {len(citation_map)} entries")
    logger.info(f"[Citation Conversion] Citation map keys: {list(citation_map.keys())}")
    
    # Convert citation placeholders to HTML citations
    def replace_citation(match):
        prefix = match.group(1) if match.group(1) else ""
        filename = match.group(2)

        logger.info(f"[Citation Conversion] Processing citation: prefix='{prefix}', filename='{filename}'")

        # Try multiple lookup strategies
        lookup_strategies = [
            filename.lower(),  # Exact filename
            filename.replace('.pdf', '').lower(),  # Without .pdf extension
            filename.lower().replace('.pdf', ''),  # Alternative order
        ]

        for strategy in lookup_strategies:
            if strategy in citation_map:
                html_citation = citation_map[strategy]
                logger.info(f"[Citation Conversion] ✅ Converted [{filename}] using strategy '{strategy}' -> HTML citation")
                return f"{prefix}{html_citation}"

        # If no mapping found, log detailed debug info
        logger.warning(f"[Citation Conversion] ❌ No mapping found for [{filename}]")
        logger.warning(f"[Citation Conversion] Tried strategies: {lookup_strategies}")
        logger.warning(f"[Citation Conversion] Available keys: {list(citation_map.keys())}")
        return match.group(0)  # Return original if no mapping found
    
    # Enhanced citation replacement with multiple patterns
    patterns_replaced = 0
    original_text = text
    
    # Pattern 1: Standard placeholder format [filename.pdf]
    citation_pattern1 = r'((?:According to|As stated in|Based on|From|In|The|As per|Per)\s+)?\[([^\]]+)\]'
    text = re.sub(citation_pattern1, replace_citation, text)
    patterns_replaced += len(re.findall(citation_pattern1, original_text))
    
    # Pattern 2: Direct filename references without brackets
    def replace_direct_filename(match):
        prefix = match.group(1) if match.group(1) else ""
        filename = match.group(2)
        
        # Clean up filename for lookup
        lookup_filename = filename.replace('.pdf', '').lower()
        
        if lookup_filename in citation_map:
            html_citation = citation_map[lookup_filename]
            logger.info(f"[Citation Conversion] ✅ Converted direct reference '{filename}' -> HTML citation")
            return f"{prefix}{html_citation}"
        else:
            return match.group(0)  # Return original if no mapping found
    
    # Pattern 2: "According to filename.pdf" (without brackets)
    citation_pattern2 = r'((?:According to|As stated in|Based on|From|In|The|As per|Per)\s+)([a-zA-Z0-9_\-\.]+\.pdf)'
    text = re.sub(citation_pattern2, replace_direct_filename, text, flags=re.IGNORECASE)
    patterns_replaced += len(re.findall(citation_pattern2, original_text, re.IGNORECASE))
    
    # Pattern 3: "The filename.pdf document/shows/indicates"
    citation_pattern3 = r'(The\s+)([a-zA-Z0-9_\-\.]+\.pdf)(\s+(?:document|shows|indicates|reports|states|mentions))'
    def replace_document_reference(match):
        prefix = match.group(1)
        filename = match.group(2)
        suffix = match.group(3)
        
        lookup_filename = filename.replace('.pdf', '').lower()
        
        if lookup_filename in citation_map:
            html_citation = citation_map[lookup_filename]
            logger.info(f"[Citation Conversion] ✅ Converted document reference '{filename}' -> HTML citation")
            return f"{prefix}{html_citation}{suffix}"
        else:
            return match.group(0)
    
    text = re.sub(citation_pattern3, replace_document_reference, text, flags=re.IGNORECASE)
    patterns_replaced += len(re.findall(citation_pattern3, original_text, re.IGNORECASE))
    
    if patterns_replaced > 0:
        logger.info(f"[Citation Conversion] ✅ Successfully converted {patterns_replaced} citation patterns")
    else:
        logger.warning(f"[Citation Conversion] No citation patterns found to convert")
        logger.warning(f"[Citation Conversion] Available mappings: {list(citation_map.keys())}")

        # FALLBACK: Try to detect and fix common citation issues
        logger.info(f"[Citation Conversion] Attempting fallback citation detection...")

        # Look for any PDF filenames mentioned in the text
        pdf_mentions = re.findall(r'([a-zA-Z0-9_\-\.]+\.pdf)', text, re.IGNORECASE)
        if pdf_mentions:
            logger.info(f"[Citation Conversion] Found PDF mentions: {pdf_mentions}")

            # Try to convert these to proper citations
            for pdf_mention in pdf_mentions:
                lookup_key = pdf_mention.replace('.pdf', '').lower()
                if lookup_key in citation_map:
                    # Replace the bare filename with a proper citation
                    html_citation = citation_map[lookup_key]
                    # Only replace if it's not already in a link
                    if f'>{pdf_mention}<' not in text and f'href=' not in text[max(0, text.find(pdf_mention)-50):text.find(pdf_mention)+50]:
                        text = text.replace(pdf_mention, html_citation)
                        logger.info(f"[Citation Conversion] ✅ Fallback converted bare filename '{pdf_mention}' to HTML citation")
                        patterns_replaced += 1

        if patterns_replaced == 0:
            logger.warning(f"[Citation Conversion] ❌ No citations could be converted even with fallback")

    return text

def validate_citations_present(answer: str, docs: List[Document], anti_hallucination_mode: str) -> Dict[str, any]:
    """
    Validate that citations are present in the answer when context documents are available.

    Args:
        answer: The AI-generated answer
        docs: List of context documents that were provided
        anti_hallucination_mode: The current anti-hallucination mode

    Returns:
        Dictionary with validation results
    """
    logger.info(f"[Citation Validation] Starting validation with {len(docs)} documents")
    logger.info(f"[Citation Validation] Answer preview: {answer[:200]}...")

    # If no documents were provided, citations aren't expected
    if not docs:
        logger.info(f"[Citation Validation] No documents provided, citations not required")
        return {'valid': True, 'warning': None}

    # Check if the answer indicates insufficient information
    insufficient_info_phrases = [
        "I don't have enough information",
        "there isn't enough information",
        "The provided context does not contain",
        "There is no information",
        "The context doesn't mention",
        "I cannot find information"
    ]

    answer_lower = answer.lower()
    has_insufficient_info = any(phrase.lower() in answer_lower for phrase in insufficient_info_phrases)

    # If answer indicates insufficient info, citations aren't expected
    if has_insufficient_info:
        logger.info(f"[Citation Validation] Answer indicates insufficient info, citations not required")
        return {'valid': True, 'warning': None}

    # Check for citation patterns in the answer
    citation_patterns = [
        (r'\[([^\]]+\.pdf)\]', "Bracket notation"),  # [filename.pdf]
        (r'<a[^>]*href="[^"]*"[^>]*>([^<]+)</a>', "HTML links"),  # HTML links
        (r'According to ([^,.\n]+\.pdf)', "According to pattern"),  # "According to filename.pdf"
        (r'As stated in ([^,.\n]+\.pdf)', "As stated in pattern"),  # "As stated in filename.pdf"
        (r'Based on ([^,.\n]+\.pdf)', "Based on pattern"),  # "Based on filename.pdf"
        (r'/download_gated/[^"\s>]+\.pdf', "Download links"),  # Direct download links
    ]

    citations_found = []
    for pattern, pattern_name in citation_patterns:
        matches = re.findall(pattern, answer, re.IGNORECASE)
        if matches:
            logger.info(f"[Citation Validation] Found {len(matches)} matches for {pattern_name}: {matches}")
            citations_found.extend(matches)

    # Check if we found any citations
    if citations_found:
        logger.info(f"[Citation Validation] ✅ Found {len(citations_found)} total citations: {citations_found}")
        return {'valid': True, 'warning': None}
    else:
        # No citations found but we have context documents
        available_docs = [doc.metadata.get('citation_filename', 'unknown') for doc in docs if doc.metadata.get('type') == 'pdf']
        warning = f"No citations found in answer despite having {len(available_docs)} context documents available: {available_docs}"
        logger.warning(f"[Citation Validation] ❌ {warning}")
        logger.warning(f"[Citation Validation] Full answer for debugging: {answer}")
        return {'valid': False, 'warning': warning}
