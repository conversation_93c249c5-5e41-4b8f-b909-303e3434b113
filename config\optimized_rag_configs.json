{"model_recommendations": {"best_for_pdf_rag": {"top_tier": ["qwen3:14b-q4_K_M", "gemma3:12b-it-q4_K_M", "deepseek-r1:8b-llama-distill-q8_0"], "excellent": ["llama3.1:8b-instruct-q8_0", "deepseek-r1:8b-llama-distill-q4_K_M", "mistral:7b-instruct-v0.3-q8_0"], "good": ["mistral:7b-instruct-v0.3-q4_K_M", "llama3.1:8b-instruct-q4_K_M", "gemma3:4b-it-q4_K_M"], "suitable": ["llama3.2:3b-instruct-q4_K_M"], "not_recommended": ["gpt-oss:20b"]}}, "optimized_configurations": {"large_models_14b_plus": {"models": ["qwen3:14b-q4_K_M"], "anti_hallucination_modes": {"strict": {"temperature": 0.05, "num_ctx": 8192, "num_predict": 2048, "top_p": 0.6, "top_k": 1, "repeat_penalty": 1.15, "stop": ["<|endoftext|>", "<|im_end|>", "\n\nHuman:", "\n\nQuestion:"], "system_prompt": "You are a precise research assistant for ERDB. Answer ONLY based on provided context. ALWAYS cite sources using [filename.pdf]. Use scientific rigor and be comprehensive in technical explanations."}, "balanced": {"temperature": 0.3, "num_ctx": 8192, "num_predict": 2048, "top_p": 0.8, "top_k": 25, "repeat_penalty": 1.1, "stop": ["<|endoftext|>", "<|im_end|>"], "system_prompt": "You are a knowledgeable ERDB assistant. Answer primarily from context with reasonable inferences. ALWAYS cite sources [filename.pdf]. Explain complex concepts clearly."}, "off": {"temperature": 0.7, "num_ctx": 8192, "num_predict": 2048, "top_p": 0.9, "top_k": 50, "repeat_penalty": 1.05, "system_prompt": "You are an expert ERDB consultant. Use context as foundation, supplement with expertise. Cite sources [filename.pdf] for context-based claims."}}, "query_parameters": {"retrieval_k": 15, "relevance_threshold": 0.12, "min_documents": 4, "max_documents": 10, "max_vision_context_length": 3000}}, "medium_models_8b_12b": {"models": ["llama3.1:8b-instruct-q8_0", "llama3.1:8b-instruct-q4_K_M", "deepseek-r1:8b-llama-distill-q8_0", "deepseek-r1:8b-llama-distill-q4_K_M", "gemma3:12b-it-q4_K_M"], "anti_hallucination_modes": {"strict": {"temperature": 0.1, "num_ctx": 6144, "num_predict": 1536, "top_p": 0.7, "top_k": 1, "repeat_penalty": 1.2, "stop": ["<|endoftext|>", "<|im_end|>", "Human:", "Question:"], "system_prompt": "You are a helpful ERDB assistant. Answer EXCLUSIVELY from provided context. ALWAYS cite sources using [filename.pdf]. Be precise and factual."}, "balanced": {"temperature": 0.4, "num_ctx": 6144, "num_predict": 1536, "top_p": 0.85, "top_k": 30, "repeat_penalty": 1.15, "stop": ["<|endoftext|>", "<|im_end|>"], "system_prompt": "You are a knowledgeable ERDB assistant. Answer from context with limited inference. ALWAYS cite sources [filename.pdf]. Be helpful and informative."}, "off": {"temperature": 0.8, "num_ctx": 6144, "num_predict": 1536, "top_p": 0.92, "top_k": 45, "repeat_penalty": 1.1, "system_prompt": "You are an expert ERDB consultant. Use context and general knowledge. Cite context sources [filename.pdf]. Distinguish between context and general knowledge."}}, "query_parameters": {"retrieval_k": 12, "relevance_threshold": 0.15, "min_documents": 3, "max_documents": 8, "max_vision_context_length": 2500}}, "small_efficient_models": {"models": ["mistral:7b-instruct-v0.3-q8_0", "mistral:7b-instruct-v0.3-q4_K_M", "gemma3:4b-it-q4_K_M"], "anti_hallucination_modes": {"strict": {"temperature": 0.15, "num_ctx": 4096, "num_predict": 1024, "top_p": 0.75, "top_k": 1, "repeat_penalty": 1.25, "stop": ["<|endoftext|>", "[INST]", "</s>"], "system_prompt": "Answer ONLY from provided context. Cite sources [filename.pdf]. Be concise and accurate."}, "balanced": {"temperature": 0.5, "num_ctx": 4096, "num_predict": 1024, "top_p": 0.88, "top_k": 35, "repeat_penalty": 1.2, "stop": ["<|endoftext|>", "</s>"], "system_prompt": "Answer from context with careful inference. Cite sources [filename.pdf]. Be helpful and clear."}, "off": {"temperature": 0.9, "num_ctx": 4096, "num_predict": 1024, "top_p": 0.95, "top_k": 55, "repeat_penalty": 1.15, "system_prompt": "Use context and knowledge. Cite context sources [filename.pdf]. Be informative and engaging."}}, "query_parameters": {"retrieval_k": 10, "relevance_threshold": 0.18, "min_documents": 3, "max_documents": 6, "max_vision_context_length": 2000}}, "compact_models": {"models": ["llama3.2:3b-instruct-q4_K_M"], "anti_hallucination_modes": {"strict": {"temperature": 0.2, "num_ctx": 3072, "num_predict": 768, "top_p": 0.8, "top_k": 1, "repeat_penalty": 1.3, "stop": ["<|endoftext|>", "Human:", "Q:"], "system_prompt": "Answer from context only. Cite [filename.pdf]. Be brief and accurate."}, "balanced": {"temperature": 0.6, "num_ctx": 3072, "num_predict": 768, "top_p": 0.9, "top_k": 40, "repeat_penalty": 1.25, "stop": ["<|endoftext|>"], "system_prompt": "Answer from context with basic inference. Cite [filename.pdf]. Be clear and concise."}, "off": {"temperature": 1.0, "num_ctx": 3072, "num_predict": 768, "top_p": 0.95, "top_k": 60, "repeat_penalty": 1.2, "system_prompt": "Use context and knowledge. Cite context [filename.pdf]. Be helpful within limits."}}, "query_parameters": {"retrieval_k": 8, "relevance_threshold": 0.22, "min_documents": 2, "max_documents": 5, "max_vision_context_length": 1500}}}, "model_specific_notes": {"qwen3:14b-q4_K_M": {"strengths": ["Excellent reasoning", "Strong context understanding", "Good technical writing"], "optimal_use": "Best overall choice for complex PDF RAG tasks", "context_window": "Can handle larger document contexts effectively"}, "gemma3:12b-it-q4_K_M": {"strengths": ["Balanced performance", "Good instruction following", "Stable responses"], "optimal_use": "Reliable choice for consistent PDF RAG performance"}, "deepseek-r1:8b-llama-distill-q8_0": {"strengths": ["Strong reasoning", "Good at technical content", "Fast inference"], "optimal_use": "Excellent for technical/scientific PDFs"}, "llama3.1:8b-instruct-q8_0": {"strengths": ["Well-rounded", "Good at following instructions", "Stable"], "optimal_use": "Solid default choice for PDF RAG"}, "mistral:7b-instruct-v0.3-q8_0": {"strengths": ["Efficient", "Good at structured responses", "Fast"], "optimal_use": "Good for performance-critical deployments"}, "llama3.2:3b-instruct-q4_K_M": {"strengths": ["Very fast", "Low memory usage", "Decent for size"], "optimal_use": "Resource-constrained environments", "limitations": ["May struggle with complex reasoning", "Limited context handling"]}, "gpt-oss:20b": {"issues": ["Often produces inconsistent results", "May not follow instructions well", "Resource intensive without benefits"], "recommendation": "Consider replacing with qwen3:14b-q4_K_M or gemma3:12b-it-q4_K_M"}}, "performance_optimizations": {"retrieval_tuning": {"high_precision_docs": {"retrieval_k": 15, "relevance_threshold": 0.25, "min_documents": 4}, "broad_coverage": {"retrieval_k": 20, "relevance_threshold": 0.12, "min_documents": 3}, "fast_response": {"retrieval_k": 8, "relevance_threshold": 0.2, "min_documents": 2}}, "hallucination_detection": {"strict": {"threshold_strict": 0.95, "threshold_balanced": 0.85, "min_statement_length": 15}, "balanced": {"threshold_strict": 0.9, "threshold_balanced": 0.7, "min_statement_length": 20}, "lenient": {"threshold_strict": 0.8, "threshold_balanced": 0.6, "min_statement_length": 25}}}, "deployment_recommendations": {"production_stack": {"primary": "qwen3:14b-q4_K_M", "fallback": "llama3.1:8b-instruct-q8_0", "fast_option": "mistral:7b-instruct-v0.3-q8_0"}, "resource_constraints": {"low_memory": "gemma3:4b-it-q4_K_M", "very_low_memory": "llama3.2:3b-instruct-q4_K_M", "balanced": "llama3.1:8b-instruct-q4_K_M"}, "specialized_tasks": {"technical_documents": "deepseek-r1:8b-llama-distill-q8_0", "general_knowledge": "gemma3:12b-it-q4_K_M", "fast_queries": "mistral:7b-instruct-v0.3-q8_0"}}}