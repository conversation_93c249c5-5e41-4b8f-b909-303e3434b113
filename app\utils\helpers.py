import os
import logging
import shutil
import hashlib
import time
import random
import json
import sqlite3
from app.services.vector_db import get_vector_db
from werkzeug.utils import secure_filename
from datetime import datetime
from app.utils.content_db import get_db_connection, get_pdf_by_original_filename
from app.utils.database import delete_pdf_locations
import spacy
import re

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Get the directory where this script is located
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
# Go up to the project root
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.dirname(BASE_DIR)))

# Environment variables
TEMP_FOLDER = os.getenv("TEMP_FOLDER", os.path.join(PROJECT_ROOT, "data", "temp"))
CHROMA_PATH = os.getenv("CHROMA_PATH", "./data/chroma/chroma")

# Load ScispaCy CRAFT model at module level
try:
    _scispacy_nlp = spacy.load("en_ner_craft_md")
except Exception as e:
    _scispacy_nlp = None
    logging.warning(f"ScispaCy CRAFT model not loaded: {e}")

def safe_remove_file(file_path, max_retries=5, initial_delay=0.1):
    """
    Safely remove a file with retry logic for handling locked files.
    Includes process detection and improved error handling.

    Args:
        file_path: Path to the file to remove
        max_retries: Maximum number of retry attempts
        initial_delay: Initial delay between retries in seconds

    Returns:
        bool: True if successful, False otherwise
    """
    if not os.path.exists(file_path):
        logger.debug(f"File {file_path} does not exist, skipping removal")
        return True

    retry_count = 0
    retry_delay = initial_delay

    while retry_count <= max_retries:
        try:
            # Try to remove the file
            os.remove(file_path)
            logger.info(f"Successfully removed file: {file_path}")
            return True
        except PermissionError as e:
            # File is locked by another process
            retry_count += 1
            if retry_count <= max_retries:
                logger.warning(f"File {file_path} is locked by another process. Retrying in {retry_delay:.2f}s ({retry_count}/{max_retries})")
                
                # Try to identify which process might be holding the file (Windows)
                try:
                    import psutil
                    for proc in psutil.process_iter(['pid', 'name', 'open_files']):
                        try:
                            if proc.info['open_files']:
                                for file_info in proc.info['open_files']:
                                    if file_path in str(file_info.path):
                                        logger.info(f"File {file_path} is being used by process {proc.info['name']} (PID: {proc.info['pid']})")
                                        break
                        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                            continue
                except ImportError:
                    logger.debug("psutil not available, skipping process detection")
                except Exception as proc_error:
                    logger.debug(f"Process detection failed: {str(proc_error)}")
                
                time.sleep(retry_delay)
                # Exponential backoff with jitter
                retry_delay = min(retry_delay * 2 * (0.5 + random.random()), 10.0)  # Cap at 10 seconds
            else:
                logger.error(f"Failed to remove file {file_path} after {max_retries} attempts: {str(e)}")
                return False
        except OSError as e:
            # Handle other OS-specific errors
            if e.errno == 2:  # No such file or directory
                logger.debug(f"File {file_path} no longer exists")
                return True
            elif e.errno == 13:  # Permission denied
                retry_count += 1
                if retry_count <= max_retries:
                    logger.warning(f"Permission denied for {file_path}. Retrying in {retry_delay:.2f}s ({retry_count}/{max_retries})")
                    time.sleep(retry_delay)
                    retry_delay = min(retry_delay * 2 * (0.5 + random.random()), 10.0)
                else:
                    logger.error(f"Failed to remove file {file_path} after {max_retries} attempts: Permission denied")
                    return False
            else:
                logger.error(f"OS error removing file {file_path}: {str(e)}")
                return False
        except Exception as e:
            logger.error(f"Unexpected error removing file {file_path}: {str(e)}")
            return False

    return False

def safe_remove_directory(dir_path, max_retries=5, initial_delay=0.1):
    """
    Safely remove a directory with retry logic for handling locked files.

    Args:
        dir_path: Path to the directory to remove
        max_retries: Maximum number of retry attempts
        initial_delay: Initial delay between retries in seconds

    Returns:
        bool: True if successful, False otherwise
    """
    if not os.path.exists(dir_path) or not os.path.isdir(dir_path):
        return True

    retry_count = 0
    retry_delay = initial_delay

    while retry_count <= max_retries:
        try:
            shutil.rmtree(dir_path)
            logger.info(f"Successfully removed directory: {dir_path}")
            return True
        except (PermissionError, OSError) as e:
            # Directory or a file within it is locked by another process
            retry_count += 1
            if retry_count <= max_retries:
                logger.warning(f"Directory {dir_path} has locked files. Retrying in {retry_delay:.2f}s ({retry_count}/{max_retries})")
                time.sleep(retry_delay)
                # Exponential backoff with jitter
                retry_delay = retry_delay * 2 * (0.5 + random.random())
            else:
                logger.error(f"Failed to remove directory {dir_path} after {max_retries} attempts: {str(e)}")
                return False
        except Exception as e:
            logger.error(f"Error removing directory {dir_path}: {str(e)}")
            return False

    return False

def allowed_file(filename: str) -> bool:
    return filename.lower().endswith(".pdf")

def delete_file(category: str, filename: str):
    """
    Delete a file and all its associated resources (images, tables, vector embeddings).
    This function handles both the hierarchical directory structure and any legacy paths.
    Includes comprehensive database cleanup and space reclamation.

    Args:
        category: The category the file belongs to
        filename: The filename to delete

    Returns:
        Tuple of (success, message)
    """
    try:
        resources_deleted = []
        pdf_name_base = os.path.splitext(filename)[0]  # Remove extension
        
        # Calculate the correct directory name by removing ocr_ or non_ocr_ prefixes
        # The directory structure uses the base name without these prefixes
        if pdf_name_base.startswith('non_ocr_'):
            pdf_dir_name = pdf_name_base[8:]  # Remove 'non_ocr_' prefix
        elif pdf_name_base.startswith('ocr_'):
            pdf_dir_name = pdf_name_base[4:]  # Remove 'ocr_' prefix
        else:
            pdf_dir_name = pdf_name_base

        # Check both possible category paths (matching list_files logic)
        category_paths = [
            os.path.join(TEMP_FOLDER, category),  # Standard path
            os.path.join(TEMP_FOLDER, "_temp", category)  # Legacy path where existing data is
        ]

        for category_path in category_paths:
            if not os.path.exists(category_path):
                continue

            logger.info(f"Checking for files in: {category_path}")

            # Check for the new directory structure first
            pdf_dir = os.path.join(category_path, pdf_dir_name)

            # If the PDF directory exists in the new structure, delete the entire directory
            if os.path.exists(pdf_dir) and os.path.isdir(pdf_dir):
                # Delete the entire PDF directory (including the PDF file, images, tables, and thumbnails)
                if safe_remove_directory(pdf_dir):
                    resources_deleted.append("PDF directory with all resources (including thumbnails)")
                    logger.info(f"Deleted PDF directory with all resources: {pdf_dir}")
                else:
                    # If directory removal fails, try to delete individual files
                    logger.warning(f"Failed to remove entire directory {pdf_dir}, attempting to delete individual files")

                    # Try to delete the PDF file first
                    pdf_file_path = os.path.join(pdf_dir, filename)
                    if os.path.exists(pdf_file_path) and safe_remove_file(pdf_file_path):
                        resources_deleted.append("PDF file")

                    # Try to delete images directory
                    images_dir = os.path.join(pdf_dir, "pdf_images")
                    if os.path.exists(images_dir) and safe_remove_directory(images_dir):
                        resources_deleted.append("PDF images")

                    # Try to delete tables directory
                    tables_dir = os.path.join(pdf_dir, "pdf_tables")
                    if os.path.exists(tables_dir) and safe_remove_directory(tables_dir):
                        resources_deleted.append("PDF tables")
            else:
                # Fall back to the old structure if the new one doesn't exist
                # Delete the PDF file
                file_path = os.path.join(category_path, filename)
                if os.path.exists(file_path):
                    if safe_remove_file(file_path):
                        resources_deleted.append("file")
                        logger.info(f"Deleted file from filesystem: {file_path}")
                    else:
                        logger.warning(f"Failed to delete file: {file_path}")
                else:
                    logger.info(f"File not found on filesystem: {file_path}")

                # Check for old structure images and tables
                old_images_dir = os.path.join(TEMP_FOLDER, "pdf_images", category)
                if os.path.exists(old_images_dir):
                    images_deleted = False
                    for img_file in os.listdir(old_images_dir):
                        if img_file.startswith(pdf_name_base):
                            img_path = os.path.join(old_images_dir, img_file)
                            os.remove(img_path)
                            images_deleted = True
                            logger.info(f"Deleted associated image: {img_path}")

                    if images_deleted:
                        resources_deleted.append("extracted images (old structure)")

                old_tables_dir = os.path.join(TEMP_FOLDER, "pdf_tables", category)
                if os.path.exists(old_tables_dir):
                    tables_deleted = False
                    for table_file in os.listdir(old_tables_dir):
                        if table_file.startswith(pdf_name_base):
                            table_path = os.path.join(old_tables_dir, table_file)
                            os.remove(table_path)
                            tables_deleted = True
                            logger.info(f"Deleted associated table: {table_path}")

                    if tables_deleted:
                        resources_deleted.append("extracted tables (old structure)")

        # Delete from vector database (regardless of directory structure)
        try:
            logger.info(f"Starting vector deletion for {filename} in category {category}")
            
            # Use the updated delete_vector_embeddings function
            vector_deletion_success = delete_vector_embeddings(category, filename)
            
            if vector_deletion_success:
                resources_deleted.append("vector data")
                logger.info(f"Successfully deleted vector embeddings for {filename} in category {category}")
                
                # Run VACUUM on ChromaDB after vector deletion to reclaim space
                try:
                    logger.info("Running VACUUM on ChromaDB after vector deletion...")
                    vacuum_success, size_before, size_after, space_reclaimed = vacuum_chromadb()
                    if vacuum_success and space_reclaimed > 0:
                        resources_deleted.append(f"reclaimed {space_reclaimed:.2f} MB from ChromaDB")
                        logger.info(f"ChromaDB VACUUM completed: reclaimed {space_reclaimed:.2f} MB")
                    else:
                        logger.info("ChromaDB VACUUM completed (no space reclaimed)")
                except Exception as vacuum_e:
                    logger.warning(f"ChromaDB VACUUM failed after vector deletion: {str(vacuum_e)}")
            else:
                logger.warning(f"Vector deletion failed for {filename} in category {category}, but continuing with file deletion")
                
        except Exception as e:
            logger.error(f"Failed to delete vector data for {filename} in category {category}: {str(e)}")
            import traceback
            logger.error(f"Vector deletion traceback: {traceback.format_exc()}")
            # Continue with file deletion even if vector deletion fails

        # Delete associated location data
        try:
            if delete_pdf_locations(filename, category):
                resources_deleted.append("location data")
                logger.info(f"Deleted location data for PDF: {filename}")
        except Exception as e:
            logger.warning(f"Failed to delete location data for {filename}: {str(e)}")

        # Check if category is now empty and clean up chat data if needed
        try:
            cleanup_result = cleanup_empty_category_data(category)
            if cleanup_result.get('chat_cleaned', False):
                resources_deleted.append("orphaned chat data")
                logger.info(f"Cleaned up orphaned chat data for category {category}: {cleanup_result['message']}")
        except Exception as e:
            logger.warning(f"Failed to check and clean up category data for {category}: {str(e)}")

        # Run comprehensive database cleanup after file deletion
        try:
            logger.info("Running comprehensive database cleanup after file deletion...")
            from app.utils.health_monitor import get_health_monitor
            monitor = get_health_monitor()
            
            # Clean up main database
            main_db_cleanup = monitor._cleanup_single_database("./erdb_main.db")
            if main_db_cleanup['success'] and main_db_cleanup['space_reclaimed_mb'] > 0:
                resources_deleted.append(f"reclaimed {main_db_cleanup['space_reclaimed_mb']:.2f} MB from main database")
                logger.info(f"Main database cleanup completed: reclaimed {main_db_cleanup['space_reclaimed_mb']:.2f} MB")
            
            # Clean up chat history database
            chat_db_cleanup = monitor._cleanup_single_database("./chat_history.db")
            if chat_db_cleanup['success'] and chat_db_cleanup['space_reclaimed_mb'] > 0:
                resources_deleted.append(f"reclaimed {chat_db_cleanup['space_reclaimed_mb']:.2f} MB from chat database")
                logger.info(f"Chat database cleanup completed: reclaimed {chat_db_cleanup['space_reclaimed_mb']:.2f} MB")
                
        except Exception as cleanup_e:
            logger.warning(f"Database cleanup failed after file deletion: {str(cleanup_e)}")

        # Prepare success message
        if resources_deleted:
            return True, f"File {filename} deleted successfully with all {', '.join(resources_deleted)}"
        else:
            return True, f"File {filename} deleted successfully"
    except Exception as e:
        logger.error(f"Failed to delete file {filename} in category {category}: {str(e)}")
        return False, f"Failed to delete file: {str(e)}"

def delete_vector_embeddings(category: str, filename: str):
    """
    Delete vector embeddings for a specific file from unified ChromaDB.
    Includes conflict resolution and improved error handling.

    Args:
        category: The category the file belongs to
        filename: The filename to delete

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        logger.info(f"Starting vector deletion for {filename} in category {category}")
        
        # Use the unified vector database service with conflict resolution
        from app.services.unified_vector_db import get_unified_vector_db, reset_unified_vector_db
        unified_db = get_unified_vector_db()
        
        try:
            # Access the underlying ChromaDB collection directly
            client = unified_db._get_db()._client
            collection = client.get_collection(name="unified_collection")
        except Exception as db_error:
            logger.warning(f"Failed to access ChromaDB collection: {str(db_error)}")
            logger.info("Attempting to reset and reinitialize ChromaDB instance...")
            
            # Try to reset the instance and retry
            try:
                reset_unified_vector_db()
                unified_db = get_unified_vector_db()
                client = unified_db._get_db()._client
                collection = client.get_collection(name="unified_collection")
                logger.info("Successfully reinitialized ChromaDB instance")
            except Exception as reset_error:
                logger.error(f"Failed to reset ChromaDB instance: {str(reset_error)}")
                return False
        
        # Create the correct deletion filter for unified ChromaDB using operators
        delete_filter = {
            "$and": [
                {"category": {"$eq": category}},
                {"source": {"$eq": filename}}
            ]
        }
        
        # Get count before deletion for verification
        try:
            before_count = collection.count()
            logger.info(f"Found {before_count} total documents in collection")
        except Exception as count_error:
            logger.warning(f"Could not get collection count: {str(count_error)}")
            before_count = 0
        
        # Check for documents matching our filter
        try:
            results = collection.get(where=delete_filter, include=['metadatas'])
            matching_count = len(results.get('ids', []))
            logger.info(f"Found {matching_count} documents matching filter: {delete_filter}")
            
            if matching_count == 0:
                logger.warning(f"No vector embeddings found for {filename} in category {category}")
                return True  # Consider it successful if nothing to delete
            
        except Exception as search_e:
            logger.warning(f"Could not search for existing documents: {str(search_e)}")
        
        # Perform the deletion
        try:
            collection.delete(where=delete_filter)
            logger.info(f"Successfully deleted vector embeddings for {filename} in category {category}")
        except Exception as delete_error:
            logger.error(f"Failed to delete vector embeddings: {str(delete_error)}")
            return False
        
        # Verify deletion
        try:
            after_count = collection.count()
            deleted_count = before_count - after_count
            logger.info(f"Verified deletion: {deleted_count} vector embeddings removed")
        except Exception as verify_error:
            logger.warning(f"Could not verify deletion: {str(verify_error)}")
            deleted_count = 0
        
        # If we deleted embeddings, run VACUUM to reclaim space
        if deleted_count > 0:
            try:
                logger.info("Running VACUUM to reclaim space after vector deletion...")
                vacuum_chromadb()
                logger.info("VACUUM completed successfully")
            except Exception as vacuum_e:
                logger.warning(f"VACUUM operation failed after vector deletion: {str(vacuum_e)}")
        
        return True
        
    except Exception as e:
        logger.error(f"Failed to delete vector embeddings for {filename} in category {category}: {str(e)}")
        import traceback
        logger.error(f"Vector deletion traceback: {traceback.format_exc()}")
        return False

def list_parent_categories():
    """Return a list of parent categories with their child categories."""
    try:
        import sqlite3
        from app.models.schema import DB_PATH
        
        # Check if database exists
        if not os.path.exists(DB_PATH):
            logger.warning(f"Database {DB_PATH} does not exist, returning empty list")
            return []
        
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        # Check if parent_categories table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='parent_categories'")
        if not cursor.fetchone():
            logger.warning("parent_categories table does not exist, returning empty list")
            conn.close()
            return []
        
        # Get all parent categories with their child categories
        cursor.execute('''
            SELECT 
                pc.id,
                pc.name,
                pc.description,
                pc.display_order,
                GROUP_CONCAT(c.name, ', ') as child_categories
            FROM parent_categories pc
            LEFT JOIN categories c ON pc.id = c.parent_category_id
            WHERE pc.is_active = 1
            GROUP BY pc.id, pc.name, pc.description, pc.display_order
            ORDER BY pc.display_order, pc.name
        ''')
        
        parent_categories = []
        for row in cursor.fetchall():
            parent_categories.append({
                'id': row[0],
                'name': row[1],
                'description': row[2],
                'display_order': row[3],
                'child_categories': row[4].split(', ') if row[4] else []
            })
        
        conn.close()
        return parent_categories
        
    except Exception as e:
        logger.error(f"Error listing parent categories: {str(e)}")
        # Fallback to file system categories if database fails
        return []

def get_categories_by_parent(parent_category_name):
    """Get all child categories for a given parent category."""
    try:
        import sqlite3
        from app.models.schema import DB_PATH
        
        # Check if database exists
        if not os.path.exists(DB_PATH):
            logger.warning(f"Database {DB_PATH} does not exist, returning empty list")
            return []
        
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        # Check if parent_categories table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='parent_categories'")
        if not cursor.fetchone():
            logger.warning("parent_categories table does not exist, returning empty list")
            conn.close()
            return []
        
        cursor.execute('''
            SELECT c.name
            FROM categories c
            JOIN parent_categories pc ON c.parent_category_id = pc.id
            WHERE pc.name = ? AND pc.is_active = 1
            ORDER BY c.name
        ''', (parent_category_name,))
        
        categories = [row[0] for row in cursor.fetchall()]
        conn.close()
        return categories
        
    except Exception as e:
        logger.error(f"Error getting categories by parent {parent_category_name}: {str(e)}")
        return []

def list_categories():
    """Return a list of parent category names for query interface (search across child categories)."""
    # For query interface, return parent categories so users can search across all child categories
    try:
        parent_categories = list_parent_categories()
        if parent_categories:
            # Return parent category names for the query dropdown
            logger.info(f"Returning parent categories for query interface: {[pc['name'] for pc in parent_categories]}")
            return [pc['name'] for pc in parent_categories]
    except Exception as e:
        logger.warning(f"Could not get parent categories from database: {str(e)}")
    
    # Fallback to child categories if parent categories fail
    return list_upload_categories()

def get_all_child_categories(parent_category_name=None):
    """Get all child categories, optionally filtered by parent category."""
    try:
        import sqlite3
        from app.models.schema import DB_PATH
        
        # Check if database exists
        if not os.path.exists(DB_PATH):
            logger.warning(f"Database {DB_PATH} does not exist, returning empty list")
            return []
        
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        # Check if parent_categories table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='parent_categories'")
        if not cursor.fetchone():
            logger.warning("parent_categories table does not exist, returning empty list")
            conn.close()
            return []
        
        if parent_category_name:
            # Get child categories for specific parent
            cursor.execute('''
                SELECT c.name
                FROM categories c
                JOIN parent_categories pc ON c.parent_category_id = pc.id
                WHERE pc.name = ? AND pc.is_active = 1
                ORDER BY c.name
            ''', (parent_category_name,))
        else:
            # Get all child categories
            cursor.execute('''
                SELECT c.name
                FROM categories c
                WHERE c.parent_category_id IS NOT NULL
                ORDER BY c.name
            ''')
        
        categories = [row[0] for row in cursor.fetchall()]
        conn.close()
        return categories
        
    except Exception as e:
        logger.error(f"Error getting child categories: {str(e)}")
        # Fallback to file system method
        return list_categories()

def list_upload_categories():
    """Return a list of all categories suitable for upload interface - includes both child categories and standalone categories."""
    try:
        import sqlite3
        from app.models.schema import DB_PATH
        
        # Check if database exists
        if not os.path.exists(DB_PATH):
            logger.warning(f"Database {DB_PATH} does not exist, falling back to file system method")
            return list_categories()
        
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        # Check if parent_categories table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='parent_categories'")
        if not cursor.fetchone():
            logger.warning("parent_categories table does not exist, falling back to file system method")
            conn.close()
            return list_categories()
        
        # Get all categories (both child categories and standalone categories)
        cursor.execute('''
            SELECT c.name
            FROM categories c
            ORDER BY c.name
        ''')
        
        categories = [row[0] for row in cursor.fetchall()]
        conn.close()
        
        if categories:
            return sorted(categories)
        else:
            # If no categories in database, fall back to file system method
            logger.warning("No categories found in database, falling back to file system method")
            return list_categories()
        
    except Exception as e:
        logger.error(f"Error getting upload categories: {str(e)}")
        # Fallback to file system method
        return list_categories()

def create_parent_category(name, description="", display_order=0):
    """Create a new parent category."""
    try:
        import sqlite3
        from app.models.schema import DB_PATH
        
        # Check if database exists
        if not os.path.exists(DB_PATH):
            logger.error(f"Database {DB_PATH} does not exist")
            return False
        
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        # Check if parent category already exists
        cursor.execute('SELECT id FROM parent_categories WHERE name = ?', (name,))
        if cursor.fetchone():
            logger.warning(f"Parent category '{name}' already exists")
            conn.close()
            return False
        
        cursor.execute('''
            INSERT INTO parent_categories (name, description, display_order)
            VALUES (?, ?, ?)
        ''', (name, description, display_order))
        
        conn.commit()
        conn.close()
        logger.info(f"Created parent category: {name}")
        return True
        
    except Exception as e:
        logger.error(f"Error creating parent category {name}: {str(e)}")
        return False

def create_child_category(name, parent_category_name, description=""):
    """Create a new child category under a parent category."""
    try:
        import sqlite3
        from app.models.schema import DB_PATH
        
        # Check if database exists
        if not os.path.exists(DB_PATH):
            logger.error(f"Database {DB_PATH} does not exist")
            return False
        
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        # Check if parent category exists
        cursor.execute('SELECT id FROM parent_categories WHERE name = ?', (parent_category_name,))
        parent_result = cursor.fetchone()
        if not parent_result:
            logger.error(f"Parent category '{parent_category_name}' not found")
            conn.close()
            return False
        
        parent_id = parent_result[0]
        
        # Check if child category already exists
        cursor.execute('SELECT id FROM categories WHERE name = ?', (name,))
        if cursor.fetchone():
            logger.warning(f"Child category '{name}' already exists")
            conn.close()
            return False
        
        # Create the child category
        cursor.execute('''
            INSERT INTO categories (name, description, parent_category_id)
            VALUES (?, ?, ?)
        ''', (name, description, parent_id))
        
        conn.commit()
        conn.close()
        logger.info(f"Created child category '{name}' under parent '{parent_category_name}'")
        return True
        
    except Exception as e:
        logger.error(f"Error creating child category {name}: {str(e)}")
        return False

def assign_category_to_parent(category_name, parent_category_name):
    """Assign an existing category to a parent category."""
    try:
        import sqlite3
        from app.models.schema import DB_PATH
        
        # Check if database exists
        if not os.path.exists(DB_PATH):
            logger.error(f"Database {DB_PATH} does not exist")
            return False
        
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        # Get parent category ID
        cursor.execute('SELECT id FROM parent_categories WHERE name = ?', (parent_category_name,))
        parent_result = cursor.fetchone()
        if not parent_result:
            logger.error(f"Parent category '{parent_category_name}' not found")
            return False
        
        parent_id = parent_result[0]
        
        # Check if category exists
        cursor.execute('SELECT id FROM categories WHERE name = ?', (category_name,))
        category_result = cursor.fetchone()
        if not category_result:
            logger.error(f"Category '{category_name}' not found")
            return False
        
        # Update category to assign it to parent
        cursor.execute('''
            UPDATE categories 
            SET parent_category_id = ? 
            WHERE name = ?
        ''', (parent_id, category_name))
        
        conn.commit()
        conn.close()
        logger.info(f"Assigned category '{category_name}' to parent category '{parent_category_name}'")
        return True
        
    except Exception as e:
        logger.error(f"Error assigning category {category_name} to parent {parent_category_name}: {str(e)}")
        return False

def get_parent_category_info(parent_category_name):
    """Get detailed information about a parent category including its child categories."""
    try:
        import sqlite3
        from app.models.schema import DB_PATH
        
        # Check if database exists
        if not os.path.exists(DB_PATH):
            logger.warning(f"Database {DB_PATH} does not exist, returning empty dict")
            return {}
        
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        # Check if parent_categories table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='parent_categories'")
        if not cursor.fetchone():
            logger.warning("parent_categories table does not exist, returning empty dict")
            conn.close()
            return {}
        
        # Get parent category info
        cursor.execute('''
            SELECT 
                pc.id,
                pc.name,
                pc.description,
                pc.display_order,
                GROUP_CONCAT(c.name, ', ') as child_categories
            FROM parent_categories pc
            LEFT JOIN categories c ON pc.id = c.parent_category_id
            WHERE pc.name = ? AND pc.is_active = 1
            GROUP BY pc.id, pc.name, pc.description, pc.display_order
        ''', (parent_category_name,))
        
        result = cursor.fetchone()
        conn.close()
        
        if result:
            return {
                'id': result[0],
                'name': result[1],
                'description': result[2],
                'display_order': result[3],
                'child_categories': result[4].split(', ') if result[4] else []
            }
        else:
            return {}
        
    except Exception as e:
        logger.error(f"Error getting parent category info for {parent_category_name}: {str(e)}")
        return {}

def get_parent_category_by_id(parent_id):
    """Get detailed information about a parent category by ID."""
    try:
        import sqlite3
        from app.models.schema import DB_PATH
        
        # Check if database exists
        if not os.path.exists(DB_PATH):
            logger.warning(f"Database {DB_PATH} does not exist, returning empty dict")
            return {}
        
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        # Check if parent_categories table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='parent_categories'")
        if not cursor.fetchone():
            logger.warning("parent_categories table does not exist, returning empty dict")
            conn.close()
            return {}
        
        # Get parent category info by ID
        cursor.execute('''
            SELECT 
                pc.id,
                pc.name,
                pc.description,
                pc.display_order,
                GROUP_CONCAT(c.name, ', ') as child_categories
            FROM parent_categories pc
            LEFT JOIN categories c ON pc.id = c.parent_category_id
            WHERE pc.id = ? AND pc.is_active = 1
            GROUP BY pc.id, pc.name, pc.description, pc.display_order
        ''', (parent_id,))
        
        result = cursor.fetchone()
        conn.close()
        
        if result:
            return {
                'id': result[0],
                'name': result[1],
                'description': result[2],
                'display_order': result[3],
                'child_categories': result[4].split(', ') if result[4] else []
            }
        else:
            return {}
        
    except Exception as e:
        logger.error(f"Error getting parent category info for ID {parent_id}: {str(e)}")
        return {}

def edit_parent_category(parent_id, name, description="", display_order=0):
    """Edit an existing parent category."""
    try:
        import sqlite3
        from app.models.schema import DB_PATH
        
        # Check if database exists
        if not os.path.exists(DB_PATH):
            logger.error(f"Database {DB_PATH} does not exist")
            return False
        
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        # Check if parent category exists
        cursor.execute('SELECT id FROM parent_categories WHERE id = ?', (parent_id,))
        if not cursor.fetchone():
            logger.error(f"Parent category with ID {parent_id} not found")
            conn.close()
            return False
        
        # Check if name already exists for a different parent category
        cursor.execute('SELECT id FROM parent_categories WHERE name = ? AND id != ?', (name, parent_id))
        if cursor.fetchone():
            logger.warning(f"Parent category name '{name}' already exists for another category")
            conn.close()
            return False
        
        # Update the parent category
        cursor.execute('''
            UPDATE parent_categories 
            SET name = ?, description = ?, display_order = ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        ''', (name, description, display_order, parent_id))
        
        conn.commit()
        conn.close()
        logger.info(f"Updated parent category ID {parent_id} with name '{name}'")
        return True
        
    except Exception as e:
        logger.error(f"Error updating parent category {parent_id}: {str(e)}")
        return False

def delete_parent_category(parent_id):
    """Delete a parent category and reassign its child categories."""
    try:
        import sqlite3
        from app.models.schema import DB_PATH
        
        # Check if database exists
        if not os.path.exists(DB_PATH):
            logger.error(f"Database {DB_PATH} does not exist")
            return False
        
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        # Check if parent category exists
        cursor.execute('SELECT name FROM parent_categories WHERE id = ?', (parent_id,))
        result = cursor.fetchone()
        if not result:
            logger.error(f"Parent category with ID {parent_id} not found")
            conn.close()
            return False
        
        parent_name = result[0]
        
        # Check if parent category has child categories
        cursor.execute('SELECT COUNT(*) FROM categories WHERE parent_category_id = ?', (parent_id,))
        child_count = cursor.fetchone()[0]
        
        if child_count > 0:
            # Reassign child categories to no parent (set parent_category_id to NULL)
            cursor.execute('''
                UPDATE categories 
                SET parent_category_id = NULL 
                WHERE parent_category_id = ?
            ''', (parent_id,))
            logger.info(f"Reassigned {child_count} child categories from parent category '{parent_name}'")
        
        # Delete the parent category
        cursor.execute('DELETE FROM parent_categories WHERE id = ?', (parent_id,))
        
        conn.commit()
        conn.close()
        logger.info(f"Deleted parent category ID {parent_id} with name '{parent_name}'")
        return True
        
    except Exception as e:
        logger.error(f"Error deleting parent category {parent_id}: {str(e)}")
        return False

def remove_child_from_parent(child_category_name, parent_category_name):
    """Remove a child category from its parent category by setting parent_category_id to NULL."""
    try:
        import sqlite3
        from app.models.schema import DB_PATH
        
        # Check if database exists
        if not os.path.exists(DB_PATH):
            logger.error(f"Database {DB_PATH} does not exist")
            return False
        
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        # Check if parent category exists
        cursor.execute('SELECT id FROM parent_categories WHERE name = ?', (parent_category_name,))
        parent_result = cursor.fetchone()
        if not parent_result:
            logger.error(f"Parent category '{parent_category_name}' not found")
            conn.close()
            return False
        
        parent_id = parent_result[0]
        
        # Check if child category exists and is assigned to this parent
        cursor.execute('''
            SELECT id FROM categories 
            WHERE name = ? AND parent_category_id = ?
        ''', (child_category_name, parent_id))
        child_result = cursor.fetchone()
        if not child_result:
            logger.error(f"Child category '{child_category_name}' not found or not assigned to parent '{parent_category_name}'")
            conn.close()
            return False
        
        # Remove the child category from the parent (set parent_category_id to NULL)
        cursor.execute('''
            UPDATE categories 
            SET parent_category_id = NULL 
            WHERE name = ? AND parent_category_id = ?
        ''', (child_category_name, parent_id))
        
        conn.commit()
        conn.close()
        logger.info(f"Removed child category '{child_category_name}' from parent category '{parent_category_name}'")
        return True
        
    except Exception as e:
        logger.error(f"Error removing child category '{child_category_name}' from parent '{parent_category_name}': {str(e)}")
        return False

def search_by_parent_category(parent_category_name, query):
    """Search across all child categories of a parent category."""
    child_categories = get_categories_by_parent(parent_category_name)
    if not child_categories:
        logger.warning(f"No child categories found for parent category: {parent_category_name}")
        return []
    
    # This function will be used by the query service to search across multiple categories
    # For now, return the list of child categories that should be searched
    return child_categories

def calculate_file_hash(file_obj):
    """
    Calculate SHA-256 hash of a file object.

    Args:
        file_obj: A file-like object (must support seek, read)

    Returns:
        str: Hexadecimal digest of the file hash
    """
    try:
        # Save the current position
        current_position = file_obj.tell()

        # Reset to beginning of file
        file_obj.seek(0)

        # Calculate hash
        file_hash = hashlib.sha256()
        chunk_size = 65536  # 64KB chunks

        while True:
            data = file_obj.read(chunk_size)
            if not data:
                break
            file_hash.update(data)

        # Restore the original position
        file_obj.seek(current_position)

        return file_hash.hexdigest()
    except Exception as e:
        logger.error(f"Error calculating file hash: {str(e)}")
        return None

def check_duplicate_pdf(file_obj, category):
    """
    Check if a PDF file already exists by filename or content hash.

    Args:
        file_obj: The file object to check
        category: The category to check in

    Returns:
        tuple: (is_duplicate, duplicate_info)
            - is_duplicate (bool): True if a duplicate exists
            - duplicate_info (dict): Information about the duplicate
    """
    try:
        original_filename = secure_filename(file_obj.filename)
        logger.info(f"Checking for duplicates: filename='{original_filename}', category='{category}'")

        # 1. Check for duplicate by original_filename in the database
        # This is the most reliable and primary way to check for duplicates.
        existing_pdf = get_pdf_by_original_filename(original_filename, category)
        if existing_pdf:
            logger.info(f"Duplicate found by filename: '{original_filename}' exists in category '{category}' with timestamped name '{existing_pdf['filename']}'.")
            return True, {
                'type': 'filename_match',
                'category': category,
                'filename': existing_pdf['filename'],  # Return the timestamped filename for deletion
                'original_filename': original_filename
            }

        # 2. If no filename match, check by content hash (fallback)
        # This catches files that may have been renamed but are otherwise identical.
        file_hash = calculate_file_hash(file_obj)
        if not file_hash:
            logger.warning(f"Could not calculate hash for {original_filename}, skipping content check.")
            return False, None

        # To check by hash, we must scan the filesystem.
        # This is less efficient, which is why we check the DB by name first.
        category_paths = [
            os.path.join(TEMP_FOLDER, category),
            os.path.join(TEMP_FOLDER, "_temp", category)
        ]
        
        logger.info(f"Scanning category paths for content hash matches: {category_paths}")
        
        for category_path in category_paths:
            if not os.path.exists(category_path):
                logger.info(f"Category path does not exist: {category_path}")
                continue
                
            try:
                for existing_filename in os.listdir(category_path):
                    if existing_filename.lower().endswith('.pdf'):
                        file_path = os.path.join(category_path, existing_filename)
                        try:
                            with open(file_path, 'rb') as f:
                                existing_hash = calculate_file_hash(f)
                                if existing_hash == file_hash:
                                    logger.info(f"Duplicate found by content hash: {original_filename} matches {existing_filename}")
                                    return True, {
                                        'type': 'content_match',
                                        'category': category,
                                        'filename': existing_filename,
                                        'original_filename': original_filename
                                    }
                        except Exception as e:
                            logger.warning(f"Error checking file hash for {file_path}: {e}")
                            continue
            except Exception as e:
                logger.warning(f"Error scanning directory {category_path}: {e}")
                continue
        
        logger.info(f"No duplicates found for '{original_filename}' in category '{category}'")
        return False, None
    except Exception as e:
        logger.error(f"Error checking for duplicate PDF: {e}")
        return False, None

def create_category(category_name: str) -> bool:
    """
    Create a new category with all necessary directories.
    
    Args:
        category_name: Name of the category to create
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Create main files directory in data/temp
        dest_dir = os.path.join(TEMP_FOLDER, category_name)
        os.makedirs(dest_dir, exist_ok=True)
        logger.info(f"Created files directory for category: {category_name}")
        
        # Create images directory
        IMAGES_FOLDER = os.getenv("IMAGES_FOLDER", "./data/temp/pdf_images")
        images_dir = os.path.join(IMAGES_FOLDER, category_name)
        os.makedirs(images_dir, exist_ok=True)
        logger.info(f"Created images directory for category: {category_name}")
        
        # Create tables directory
        TABLES_FOLDER = os.getenv("TABLES_FOLDER", "./data/temp/pdf_tables")
        tables_dir = os.path.join(TABLES_FOLDER, category_name)
        os.makedirs(tables_dir, exist_ok=True)
        logger.info(f"Created tables directory for category: {category_name}")
        
        logger.info(f"Category '{category_name}' created successfully with all directories")
        return True
        
    except Exception as e:
        logger.error(f"Error creating category '{category_name}': {e}")
        return False

def cleanup_category_chat_data(category_name):
    """
    Clean up chat history and analytics data for a specific category.
    This is called when all files in a category are deleted.
    
    Args:
        category_name (str): Name of the category to clean up
        
    Returns:
        dict: Result with cleanup status and counts
    """
    try:
        logger.info(f"Cleaning up chat data for category: {category_name}")
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Delete chat_analytics first (references chat_history)
        cursor.execute("DELETE FROM chat_analytics WHERE category = ?", (category_name,))
        analytics_deleted = cursor.rowcount
        
        # Delete chat_history
        cursor.execute("DELETE FROM chat_history WHERE category = ?", (category_name,))
        chat_deleted = cursor.rowcount
        
        conn.commit()
        conn.close()
        
        logger.info(f"Cleaned up {chat_deleted} chat history records and {analytics_deleted} analytics records for category: {category_name}")
        
        return {
            "success": True,
            "chat_history_deleted": chat_deleted,
            "analytics_deleted": analytics_deleted,
            "message": f"Cleaned up {chat_deleted} chat history and {analytics_deleted} analytics records for category: {category_name}"
        }
        
    except Exception as e:
        logger.error(f"Error cleaning up chat data for category {category_name}: {str(e)}")
        return {
            "success": False,
            "message": f"Error cleaning up chat data: {str(e)}"
        }

def cleanup_empty_category_data(category_name):
    """
    Check if a category is empty (no files) and clean up related chat data if needed.
    This should be called after file deletions to clean up orphaned chat data.
    
    Args:
        category_name (str): Name of the category to check and clean up
        
    Returns:
        dict: Result with cleanup status and information
    """
    try:
        logger.info(f"Checking if category {category_name} is empty and cleaning up if needed")
        
        # Check if category has any files
        data_check = check_category_has_related_data(category_name)
        
        # Check if there are any files or PDF documents
        has_files = (data_check['data_summary'].get('pdf_documents', 0) > 0 or 
                    data_check['data_summary'].get('filesystem_files', 0) > 0 or
                    data_check['data_summary'].get('legacy_filesystem_files', 0) > 0)
        
        if not has_files:
            # Category is empty, clean up chat data
            logger.info(f"Category {category_name} is empty, cleaning up chat data")
            cleanup_result = cleanup_category_chat_data(category_name)
            
            if cleanup_result['success']:
                return {
                    "success": True,
                    "category_empty": True,
                    "chat_cleaned": True,
                    "message": f"Category {category_name} was empty and chat data has been cleaned up",
                    "cleanup_details": cleanup_result
                }
            else:
                return {
                    "success": False,
                    "category_empty": True,
                    "chat_cleaned": False,
                    "message": f"Category {category_name} was empty but failed to clean up chat data: {cleanup_result['message']}"
                }
        else:
            # Category still has files
            return {
                "success": True,
                "category_empty": False,
                "chat_cleaned": False,
                "message": f"Category {category_name} still has files, no cleanup needed",
                "remaining_data": data_check['data_summary']
            }
            
    except Exception as e:
        logger.error(f"Error checking and cleaning up category {category_name}: {str(e)}")
        return {
            "success": False,
            "message": f"Error checking category: {str(e)}"
        }

def check_category_has_related_data(category_name):
    """
    Check if a category has any related data that would prevent deletion.
    
    Args:
        category_name (str): Name of the category to check
        
    Returns:
        dict: Result with has_data status and detailed information about existing data
    """
    try:
        logger.info(f"Checking for related data in category: {category_name}")
        
        # Validate category name for special characters that might cause issues
        invalid_chars = [':', ';', '<', '>', '"', "'", '\\', '/', '|', '?', '*']
        found_invalid_chars = [char for char in invalid_chars if char in category_name]
        
        if found_invalid_chars:
            error_msg = f"Category name contains invalid characters: {', '.join(found_invalid_chars)}"
            logger.warning(f"Invalid category name '{category_name}': {error_msg}")
            return {
                "has_data": True,  # Assume has data if we can't check due to invalid name
                "total_items": 0,
                "data_summary": {},
                "category_name": category_name,
                "error": error_msg,
                "invalid_chars": found_invalid_chars
            }
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Check for data in various tables
        data_summary = {}
        total_items = 0
        
        # Check pdf_documents
        cursor.execute("SELECT COUNT(*) FROM pdf_documents WHERE category = ?", (category_name,))
        pdf_count = cursor.fetchone()[0]
        data_summary['pdf_documents'] = pdf_count
        total_items += pdf_count
        
        # Check scraped_pages
        cursor.execute("SELECT COUNT(*) FROM scraped_pages WHERE category = ?", (category_name,))
        scraped_count = cursor.fetchone()[0]
        data_summary['scraped_pages'] = scraped_count
        total_items += scraped_count
        
        # Check chat_history
        cursor.execute("SELECT COUNT(*) FROM chat_history WHERE category = ?", (category_name,))
        chat_count = cursor.fetchone()[0]
        data_summary['chat_history'] = chat_count
        total_items += chat_count
        
        # Check chat_analytics
        cursor.execute("SELECT COUNT(*) FROM chat_analytics WHERE category = ?", (category_name,))
        analytics_count = cursor.fetchone()[0]
        data_summary['chat_analytics'] = analytics_count
        total_items += analytics_count
        
        # Check content_sources
        cursor.execute("SELECT COUNT(*) FROM content_sources WHERE category = ?", (category_name,))
        content_count = cursor.fetchone()[0]
        data_summary['content_sources'] = content_count
        total_items += content_count
        
        # Check vector_embeddings (ChromaDB)
        vector_count = 0
        try:
            db = get_vector_db(category_name)
            # Use the unified database approach - check for documents with this category
            from chromadb import PersistentClient
            unified_chroma_path = os.getenv("UNIFIED_CHROMA_PATH", "./data/unified_chroma")
            if os.path.exists(unified_chroma_path):
                client = PersistentClient(path=unified_chroma_path)
                try:
                    collection = client.get_collection(name="unified_collection")
                    # Get count of documents for this category
                    results = collection.get(where={"category": category_name}, include=[])
                    vector_count = len(results['ids']) if results['ids'] else 0
                except Exception as e:
                    logger.debug(f"Could not access unified collection: {str(e)}")
        except Exception as e:
            logger.debug(f"Could not check vector embeddings for category {category_name}: {str(e)}")
        
        data_summary['vector_embeddings'] = vector_count
        total_items += vector_count
        
        # Check for files in filesystem (only PDF files)
        file_count = 0
        temp_category_path = os.path.join(TEMP_FOLDER, category_name)
        if os.path.exists(temp_category_path):
            try:
                for root, dirs, files in os.walk(temp_category_path):
                    # Only count PDF files
                    pdf_files = [f for f in files if f.lower().endswith('.pdf')]
                    file_count += len(pdf_files)
            except Exception as e:
                logger.debug(f"Could not count files in {temp_category_path}: {str(e)}")
        
        data_summary['filesystem_files'] = file_count
        total_items += file_count
        
        # Check legacy temp directory (only PDF files)
        legacy_file_count = 0
        legacy_temp_category_path = os.path.join(TEMP_FOLDER, "_temp", category_name)
        if os.path.exists(legacy_temp_category_path):
            try:
                for root, dirs, files in os.walk(legacy_temp_category_path):
                    # Only count PDF files
                    pdf_files = [f for f in files if f.lower().endswith('.pdf')]
                    legacy_file_count += len(pdf_files)
            except Exception as e:
                logger.debug(f"Could not count files in {legacy_temp_category_path}: {str(e)}")
        
        data_summary['legacy_filesystem_files'] = legacy_file_count
        total_items += legacy_file_count
        
        conn.close()
        
        has_data = total_items > 0
        
        logger.info(f"Category {category_name} data check completed: {total_items} total items found")
        
        return {
            "has_data": has_data,
            "total_items": total_items,
            "data_summary": data_summary,
            "category_name": category_name
        }
        
    except Exception as e:
        logger.error(f"Error checking category data for {category_name}: {str(e)}")
        return {
            "has_data": True,  # Assume has data if we can't check
            "total_items": 0,
            "data_summary": {},
            "category_name": category_name,
            "error": str(e)
        }

def delete_category_and_files(category_name):
    """
    Delete a category and all its associated files and database entries.
    
    Args:
        category_name (str): Name of the category to delete
        
    Returns:
        dict: Result with success status and message
    """
    try:
        logger.info(f"Starting deletion of category: {category_name}")
        
        # Validate category name for special characters that might cause issues
        invalid_chars = [':', ';', '<', '>', '"', "'", '\\', '/', '|', '?', '*']
        found_invalid_chars = [char for char in invalid_chars if char in category_name]
        
        if found_invalid_chars:
            error_msg = f"Category name contains invalid characters: {', '.join(found_invalid_chars)}. Category names cannot contain: {', '.join(invalid_chars)}"
            logger.warning(f"Invalid category name '{category_name}': {error_msg}")
            return {
                "success": False,
                "message": error_msg,
                "invalid_chars": found_invalid_chars,
                "category_name": category_name
            }
        
        # First, check if category has related data
        data_check = check_category_has_related_data(category_name)
        
        if data_check["has_data"]:
            # Build detailed error message
            data_summary = data_check["data_summary"]
            error_details = []
            
            if data_summary.get('pdf_documents', 0) > 0:
                error_details.append(f"{data_summary['pdf_documents']} PDF document(s)")
            if data_summary.get('scraped_pages', 0) > 0:
                error_details.append(f"{data_summary['scraped_pages']} scraped page(s)")
            if data_summary.get('chat_history', 0) > 0:
                error_details.append(f"{data_summary['chat_history']} chat history record(s)")
            if data_summary.get('chat_analytics', 0) > 0:
                error_details.append(f"{data_summary['chat_analytics']} analytics record(s)")
            if data_summary.get('content_sources', 0) > 0:
                error_details.append(f"{data_summary['content_sources']} content source(s)")
            if data_summary.get('vector_embeddings', 0) > 0:
                error_details.append(f"{data_summary['vector_embeddings']} vector embedding(s)")
            if data_summary.get('filesystem_files', 0) > 0:
                error_details.append(f"{data_summary['filesystem_files']} file(s) in filesystem")
            if data_summary.get('legacy_filesystem_files', 0) > 0:
                error_details.append(f"{data_summary['legacy_filesystem_files']} legacy file(s)")
            
            error_message = f"Cannot delete category '{category_name}' because it contains: {', '.join(error_details)}. Please delete all related data first."
            
            logger.warning(f"Category deletion prevented for {category_name}: {error_message}")
            
            return {
                "success": False,
                "message": error_message,
                "has_related_data": True,
                "data_summary": data_summary,
                "total_items": data_check["total_items"]
            }
        
        # Proceed with deletion if no related data exists
        logger.info(f"No related data found for category {category_name}, proceeding with deletion")
        
        # 1. Delete files from temp directory
        temp_category_path = os.path.join(TEMP_FOLDER, category_name)
        if os.path.exists(temp_category_path):
            import shutil
            shutil.rmtree(temp_category_path)
            logger.info(f"Deleted temp directory: {temp_category_path}")
        
        # 2. Delete files from legacy temp directory
        legacy_temp_category_path = os.path.join(TEMP_FOLDER, "_temp", category_name)
        if os.path.exists(legacy_temp_category_path):
            import shutil
            shutil.rmtree(legacy_temp_category_path)
            logger.info(f"Deleted legacy temp directory: {legacy_temp_category_path}")
        
        # 3. Delete from database with proper foreign key handling
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Disable foreign key constraints temporarily
        cursor.execute("PRAGMA foreign_keys=OFF")
        
        try:
            # Delete in the correct order to avoid foreign key constraint violations
            
            # First, delete from chat_analytics (references chat_history)
            cursor.execute("DELETE FROM chat_analytics WHERE category = ?", (category_name,))
            analytics_deleted = cursor.rowcount
            
            # Delete from chat_history
            cursor.execute("DELETE FROM chat_history WHERE category = ?", (category_name,))
            chat_deleted = cursor.rowcount
            
            # Delete from scraped_pages
            cursor.execute("DELETE FROM scraped_pages WHERE category = ?", (category_name,))
            pages_deleted = cursor.rowcount
            
            # Delete from pdf_documents (this might have foreign key references)
            cursor.execute("DELETE FROM pdf_documents WHERE category = ?", (category_name,))
            pdf_deleted = cursor.rowcount
            
            # Delete from source_urls that are only used by this category
            # First, get all source_url_ids that are used by PDFs in this category
            cursor.execute("SELECT DISTINCT source_url_id FROM pdf_documents WHERE category = ? AND source_url_id IS NOT NULL", (category_name,))
            source_url_ids = [row[0] for row in cursor.fetchall()]
            
            # Delete source_urls that are only used by this category
            source_urls_deleted = 0
            for source_url_id in source_url_ids:
                # Check if this source_url is used by any other categories
                cursor.execute("SELECT COUNT(*) FROM pdf_documents WHERE source_url_id = ? AND category != ?", (source_url_id, category_name))
                other_usage = cursor.fetchone()[0]
                
                if other_usage == 0:
                    # This source_url is only used by the category being deleted
                    cursor.execute("DELETE FROM source_urls WHERE id = ?", (source_url_id,))
                    source_urls_deleted += cursor.rowcount
            
            # Re-enable foreign key constraints
            cursor.execute("PRAGMA foreign_keys=ON")
            
            conn.commit()
            
            logger.info(f"Database cleanup completed: {pdf_deleted} PDFs, {pages_deleted} pages, {chat_deleted} chats, {analytics_deleted} analytics, {source_urls_deleted} source URLs deleted")
            
            return {
                "success": True,
                "message": f"Category '{category_name}' and all associated files deleted successfully",
                "deleted_items": {
                    "pdfs": pdf_deleted,
                    "pages": pages_deleted,
                    "chats": chat_deleted,
                    "analytics": analytics_deleted,
                    "source_urls": source_urls_deleted
                }
            }
            
        except Exception as db_error:
            # Re-enable foreign key constraints even if there's an error
            cursor.execute("PRAGMA foreign_keys=ON")
            conn.rollback()
            raise db_error
            
    except Exception as e:
        logger.error(f"Error deleting category {category_name}: {str(e)}")
        return {
            "success": False,
            "message": f"Error deleting category: {str(e)}"
        }
    finally:
        if 'conn' in locals():
            conn.close()

def italicize_scientific_names(text):
    """
    Detect scientific names in text and wrap them in <i>...</i> tags using ScispaCy (CRAFT model).
    """
    if not _scispacy_nlp or not text:
        return text
    doc = _scispacy_nlp(text)
    # Collect entities to replace, in reverse order to avoid messing up indices
    entities = [ent for ent in doc.ents if ent.label_ in ("GENE_OR_GENE_PRODUCT", "SPECIES", "ORGANISM", "TAXON")]
    new_text = text
    offset = 0
    for ent in sorted(entities, key=lambda e: e.start_char, reverse=True):
        start, end = ent.start_char, ent.end_char
        entity_text = new_text[start:end]
        # Avoid double-wrapping
        if not (entity_text.startswith('<i>') and entity_text.endswith('</i>')):
            new_text = new_text[:start] + '<i>' + entity_text + '</i>' + new_text[end:]
    return new_text

def vacuum_chromadb():
    """
    Perform enhanced VACUUM operation on ChromaDB to reclaim unused space after deletions.
    This includes WAL checkpoint forcing and additional optimizations to prevent accumulation.
    
    Returns:
        Tuple of (success, size_before, size_after, space_reclaimed)
    """
    try:
        # Path to the unified ChromaDB SQLite file
        db_path = "./data/unified_chroma/chroma.sqlite3"
        
        if not os.path.exists(db_path):
            logger.warning(f"ChromaDB file not found at {db_path}")
            return False, 0, 0, 0
        
        # Get size before VACUUM (including WAL files)
        size_before = os.path.getsize(db_path)
        
        # Check for WAL files
        wal_path = db_path + "-wal"
        shm_path = db_path + "-shm"
        wal_size_before = os.path.getsize(wal_path) if os.path.exists(wal_path) else 0
        shm_size_before = os.path.getsize(shm_path) if os.path.exists(shm_path) else 0
        total_size_before = size_before + wal_size_before + shm_size_before
        
        logger.info(f"Before optimization - Main: {size_before/1024/1024:.2f} MB, WAL: {wal_size_before/1024/1024:.2f} MB, SHM: {shm_size_before/1024/1024:.2f} MB")
        
        # Connect to database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check journal mode
        cursor.execute("PRAGMA journal_mode")
        journal_mode = cursor.fetchone()[0]
        logger.info(f"Database journal mode: {journal_mode}")
        
        # Force WAL checkpoint if in WAL mode
        if journal_mode.lower() == 'wal':
            logger.info("Forcing WAL checkpoint...")
            cursor.execute("PRAGMA wal_checkpoint(TRUNCATE)")
            checkpoint_result = cursor.fetchone()
            logger.info(f"WAL checkpoint result: busy={checkpoint_result[0]}, log_size={checkpoint_result[1]}, checkpointed={checkpoint_result[2]}")
        
        # Get database info before optimization
        cursor.execute("PRAGMA page_count")
        pages_before = cursor.fetchone()[0]
        cursor.execute("PRAGMA freelist_count")
        free_pages_before = cursor.fetchone()[0]
        
        logger.info(f"Before VACUUM - Pages: {pages_before}, Free pages: {free_pages_before}")
        
        # Perform enhanced VACUUM operation
        logger.info("Running VACUUM operation...")
        cursor.execute("VACUUM")
        
        # Run ANALYZE to update statistics
        logger.info("Running ANALYZE operation...")
        cursor.execute("ANALYZE")
        
        # Additional optimizations
        logger.info("Running PRAGMA optimize...")
        cursor.execute("PRAGMA optimize")
        
        # Get database info after optimization
        cursor.execute("PRAGMA page_count")
        pages_after = cursor.fetchone()[0]
        cursor.execute("PRAGMA freelist_count")
        free_pages_after = cursor.fetchone()[0]
        
        logger.info(f"After VACUUM - Pages: {pages_after}, Free pages: {free_pages_after}")
        
        # Force final WAL checkpoint if in WAL mode
        if journal_mode.lower() == 'wal':
            logger.info("Final WAL checkpoint...")
            cursor.execute("PRAGMA wal_checkpoint(TRUNCATE)")
            final_checkpoint = cursor.fetchone()
            logger.info(f"Final checkpoint result: busy={final_checkpoint[0]}, log_size={final_checkpoint[1]}, checkpointed={final_checkpoint[2]}")
        
        conn.close()
        
        # Get size after VACUUM (including WAL files)
        size_after = os.path.getsize(db_path)
        wal_size_after = os.path.getsize(wal_path) if os.path.exists(wal_path) else 0
        shm_size_after = os.path.getsize(shm_path) if os.path.exists(shm_path) else 0
        total_size_after = size_after + wal_size_after + shm_size_after
        
        space_reclaimed = total_size_before - total_size_after
        
        logger.info(f"After optimization - Main: {size_after/1024/1024:.2f} MB, WAL: {wal_size_after/1024/1024:.2f} MB, SHM: {shm_size_after/1024/1024:.2f} MB")
        logger.info(f"Enhanced VACUUM completed: {total_size_before/1024/1024:.2f} MB → {total_size_after/1024/1024:.2f} MB (reclaimed {space_reclaimed/1024/1024:.2f} MB)")
        
        return True, total_size_before / (1024 * 1024), total_size_after / (1024 * 1024), space_reclaimed / (1024 * 1024)
        
    except Exception as e:
        logger.error(f"Enhanced VACUUM operation failed: {str(e)}")
        return False, 0, 0, 0

def get_files_in_category(category):
    """
    Return a list of all original_filename values for PDFs in the given category from the pdf_documents table.
    Args:
        category (str): The category to list files for.
    Returns:
        list: List of original_filename strings (or empty list if none).
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT original_filename FROM pdf_documents WHERE category = ?", (category,))
        rows = cursor.fetchall()
        return [row[0] for row in rows if row[0]]
    except Exception as e:
        logger.error(f"Error in get_files_in_category: {e}")
        return []
    finally:
        if 'conn' in locals():
            conn.close()

def update_category_name(old_category_name: str, new_category_name: str) -> bool:
    """
    Update a category name and all its associated data.
    
    Args:
        old_category_name (str): The current category name
        new_category_name (str): The new category name
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        logger.info(f"Updating category name from '{old_category_name}' to '{new_category_name}'")
        
        # Validate category names for special characters that might cause issues
        invalid_chars = [':', ';', '<', '>', '"', "'", '\\', '/', '|', '?', '*']
        
        # Check old category name
        found_invalid_chars_old = [char for char in invalid_chars if char in old_category_name]
        if found_invalid_chars_old:
            error_msg = f"Old category name contains invalid characters: {', '.join(found_invalid_chars_old)}"
            logger.warning(f"Invalid old category name '{old_category_name}': {error_msg}")
            return False
        
        # Check new category name
        found_invalid_chars_new = [char for char in invalid_chars if char in new_category_name]
        if found_invalid_chars_new:
            error_msg = f"New category name contains invalid characters: {', '.join(found_invalid_chars_new)}"
            logger.warning(f"Invalid new category name '{new_category_name}': {error_msg}")
            return False
        
        # Check if old category exists
        available_categories = list_categories()
        if old_category_name not in available_categories:
            logger.error(f"Category '{old_category_name}' not found in available categories")
            return False
        
        # Check if new category name already exists
        if new_category_name in available_categories:
            logger.error(f"Category '{new_category_name}' already exists")
            return False
        
        # 1. Update filesystem directories
        old_temp_path = os.path.join(TEMP_FOLDER, old_category_name)
        new_temp_path = os.path.join(TEMP_FOLDER, new_category_name)
        
        if os.path.exists(old_temp_path):
            try:
                os.rename(old_temp_path, new_temp_path)
                logger.info(f"Renamed temp directory from {old_temp_path} to {new_temp_path}")
            except Exception as e:
                logger.error(f"Failed to rename temp directory: {str(e)}")
                return False
        
        # 2. Update legacy temp directory
        old_legacy_temp_path = os.path.join(TEMP_FOLDER, "_temp", old_category_name)
        new_legacy_temp_path = os.path.join(TEMP_FOLDER, "_temp", new_category_name)
        
        if os.path.exists(old_legacy_temp_path):
            try:
                os.rename(old_legacy_temp_path, new_legacy_temp_path)
                logger.info(f"Renamed legacy temp directory from {old_legacy_temp_path} to {new_legacy_temp_path}")
            except Exception as e:
                logger.error(f"Failed to rename legacy temp directory: {str(e)}")
                return False
        
        # 3. Update database entries
        conn = get_db_connection()
        cursor = conn.cursor()
        
        try:
            # Update pdf_documents
            cursor.execute("UPDATE pdf_documents SET category = ? WHERE category = ?", (new_category_name, old_category_name))
            pdf_updated = cursor.rowcount
            
            # Update scraped_pages
            cursor.execute("UPDATE scraped_pages SET category = ? WHERE category = ?", (new_category_name, old_category_name))
            pages_updated = cursor.rowcount
            
            # Update chat_history
            cursor.execute("UPDATE chat_history SET category = ? WHERE category = ?", (new_category_name, old_category_name))
            chat_updated = cursor.rowcount
            
            # Update chat_analytics
            cursor.execute("UPDATE chat_analytics SET category = ? WHERE category = ?", (new_category_name, old_category_name))
            analytics_updated = cursor.rowcount
            
            # Update content_sources
            cursor.execute("UPDATE content_sources SET category = ? WHERE category = ?", (new_category_name, old_category_name))
            content_updated = cursor.rowcount
            
            conn.commit()
            
            logger.info(f"Database update completed: {pdf_updated} PDFs, {pages_updated} pages, {chat_updated} chats, {analytics_updated} analytics, {content_updated} content sources updated")
            
            return True
            
        except Exception as db_error:
            conn.rollback()
            logger.error(f"Database update failed: {str(db_error)}")
            return False
        finally:
            conn.close()
            
    except Exception as e:
        logger.error(f"Error updating category name from '{old_category_name}' to '{new_category_name}': {str(e)}")
        return False